"""
Log Tab - Content Strategist Dashboard

This module contains the LogTab class which handles the logging interface
including log display, controls, and message formatting.
"""

import tkinter as tk
from tkinter import ttk, scrolledtext
from datetime import datetime
from typing import Optional


class LogTab(ttk.Frame):
    """
    Log tab containing log display and controls.
    """

    def __init__(self, master, *args, **kwargs):
        super().__init__(master, *args, **kwargs)
        
        # Create the log UI
        self._create_widgets()

    def _create_widgets(self):
        """Create the log tab widgets."""
        # Configure padding
        self.configure(padding="8")
        
        # Log controls
        log_controls = ttk.Frame(self)
        log_controls.pack(fill=tk.X, pady=(0, 8))
        
        ttk.Button(log_controls, text="Clear Log", command=self.clear_log).pack(side=tk.LEFT)
        
        # Log text area (more compact)
        self.log_text = scrolledtext.ScrolledText(self, wrap=tk.WORD, height=20)
        self.log_text.pack(fill=tk.BOTH, expand=True)
        
        # Configure log text tags for different message types
        self.log_text.tag_configure("info", foreground="black")
        self.log_text.tag_configure("success", foreground="green")
        self.log_text.tag_configure("warning", foreground="orange")
        self.log_text.tag_configure("error", foreground="red")

    def log_message(self, message: str, level: str = "info"):
        """Add a message to the log with timestamp."""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        formatted_message = f"[{timestamp}] {message}\n"

        # Check if log_text exists (in case this is called before GUI is fully initialized)
        if hasattr(self, 'log_text'):
            self.log_text.config(state=tk.NORMAL)
            self.log_text.insert(tk.END, formatted_message, level)
            self.log_text.see(tk.END)
            self.log_text.config(state=tk.DISABLED)

        # Also print to console
        print(formatted_message.strip())

    def clear_log(self):
        """Clear the log text area."""
        try:
            self.log_text.config(state=tk.NORMAL)
            self.log_text.delete('1.0', tk.END)
            self.log_text.config(state=tk.DISABLED)
            
            # Log the clear action
            self.log_message("Log cleared", "info")
            
        except Exception as e:
            print(f"Error clearing log: {e}")

    def get_log_content(self) -> str:
        """Get the current log content as a string."""
        try:
            return self.log_text.get('1.0', tk.END)
        except Exception:
            return ""

    def save_log_to_file(self, filename: str):
        """Save the current log content to a file."""
        try:
            content = self.get_log_content()
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(content)
            self.log_message(f"Log saved to {filename}", "success")
        except Exception as e:
            self.log_message(f"Error saving log to file: {e}", "error")

    def set_log_level_filter(self, level: Optional[str] = None):
        """Set a filter for log levels (future enhancement)."""
        # This could be implemented to filter messages by level
        # For now, it's a placeholder for future functionality
        pass
