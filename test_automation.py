#!/usr/bin/env python3
"""
Test script for the automated batch execution feature.

This script tests the integration of the batch automation components
without launching the full GUI.
"""

import sys
import os
import json

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(__file__))

from database import ContentDatabase
from planner import ContentPlanner
from worker import ContentWorker
from core import config_manager

def test_batch_automation():
    """Test the batch automation components."""
    
    print("🤖 Testing Batch Automation Components")
    print("=" * 50)
    
    # Initialize components
    try:
        db = ContentDatabase()
        planner = ContentPlanner(debug_mode=True)
        worker = ContentWorker()
        print("✅ Components initialized successfully")
    except Exception as e:
        print(f"❌ Failed to initialize components: {e}")
        return False
    
    # Test database operations
    print("\n📊 Testing Database Operations...")
    try:
        stats = db.get_content_stats()
        print(f"Current content stats: {stats}")
        
        # Check for existing content
        all_content = db.get_all_content()
        print(f"Total content items: {len(all_content)}")
        
        if len(all_content) == 0:
            print("⚠️  No content found in database. Adding test content...")
            # Add some test content
            test_content = [
                ("test shaving cream", "grooming", "shaving", "Best natural shaving cream guide"),
                ("leather care tips", "leather goods", "maintenance", "Complete leather care routine"),
                ("eco friendly soap", "sustainability", "natural products", "Zero waste bathroom essentials")
            ]
            
            for keyword, pillar, craft, angle in test_content:
                content_id = db.add_content_idea(keyword, pillar, craft, angle)
                print(f"  Added test content: ID={content_id}, Keyword='{keyword}'")
        
        print("✅ Database operations working correctly")
    except Exception as e:
        print(f"❌ Database operations failed: {e}")
        return False
    
    # Test planner operations
    print("\n🎯 Testing Planner Operations...")
    try:
        # Get settings for testing
        pillar_weights = {"grooming": 1.2, "leather goods": 1.0, "sustainability": 0.8}
        freshness_threshold = 50.0  # Lower threshold for testing
        
        # Calculate scores
        scored_content = planner.calculate_freshness_scores(pillar_weights)
        print(f"Scored {len(scored_content)} content items")
        
        if scored_content:
            top_item = scored_content[0]
            print(f"Top scoring item: '{top_item['keyword']}' (Score: {top_item['freshness_score']:.1f})")
        
        # Test job selection
        next_job = planner.get_next_job(pillar_weights, freshness_threshold)
        if next_job:
            print(f"Selected job for execution: '{next_job['keyword']}'")
        else:
            print(f"No job selected (threshold: {freshness_threshold})")
        
        print("✅ Planner operations working correctly")
    except Exception as e:
        print(f"❌ Planner operations failed: {e}")
        return False
    
    # Test worker operations (basic setup check)
    print("\n🔧 Testing Worker Setup...")
    try:
        # Check if worker can be initialized
        worker_instance = ContentWorker()
        print("✅ Worker initialized successfully")
        
        # Note: We don't actually execute a job in this test since it requires API keys
        print("ℹ️  Worker execution test skipped (requires API configuration)")
    except Exception as e:
        print(f"❌ Worker setup failed: {e}")
        return False
    
    # Test configuration access
    print("\n⚙️  Testing Configuration Access...")
    try:
        freshness_threshold = config_manager.get_pipeline_setting('freshness_threshold') or 70.0
        pillar_weights_str = config_manager.get_pipeline_setting('pillar_weights') or '{}'
        print(f"Freshness threshold: {freshness_threshold}")
        print(f"Pillar weights: {pillar_weights_str}")
        print("✅ Configuration access working correctly")
    except Exception as e:
        print(f"❌ Configuration access failed: {e}")
        return False
    
    print("\n🎉 All batch automation components are working correctly!")
    print("\nNext steps:")
    print("1. Configure your API keys in config.ini")
    print("2. Generate content ideas using the Idea Generator")
    print("3. Use the new 🤖 Batch Automation controls in the Dashboard")
    print("4. Set appropriate job count and interval settings")
    print("5. Click '🚀 Start Automated Batch' to begin automation")
    
    return True

def test_database_methods():
    """Test specific database methods used by automation."""
    
    print("\n🗄️  Testing Database Methods Used by Automation...")
    
    db = ContentDatabase()
    
    # Test get_content_by_keyword
    try:
        result = db.get_content_by_keyword("test_keyword_12345")
        print(f"get_content_by_keyword (non-existent): {result}")
        
        # Add a test item and try to find it
        test_id = db.add_content_idea("automation_test_keyword", "test_pillar", "test_craft", "test angle")
        found_content = db.get_content_by_keyword("automation_test_keyword")
        if found_content:
            print(f"get_content_by_keyword (existing): Found ID {found_content['id']}")
            # Clean up
            db.delete_content(test_id)
        else:
            print("❌ Failed to find added content")
        
        print("✅ get_content_by_keyword working correctly")
    except Exception as e:
        print(f"❌ get_content_by_keyword failed: {e}")
    
    # Test add_content_idea
    try:
        content_id = db.add_content_idea("another_test", "test_pillar", "test_craft", "test angle")
        print(f"add_content_idea: Created ID {content_id}")
        # Clean up
        db.delete_content(content_id)
        print("✅ add_content_idea working correctly")
    except Exception as e:
        print(f"❌ add_content_idea failed: {e}")

if __name__ == "__main__":
    print("🚀 SEO Assistant - Batch Automation Test")
    print("This script tests the automated batch execution feature components.")
    print()
    
    success = test_batch_automation()
    test_database_methods()
    
    if success:
        print("\n✅ All tests passed! The batch automation feature is ready to use.")
        exit(0)
    else:
        print("\n❌ Some tests failed. Please check the error messages above.")
        exit(1)
