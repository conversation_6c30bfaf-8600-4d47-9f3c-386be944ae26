# Batch Automation Feature - SEO Assistant

## Overview

The automated batch execution feature allows you to run multiple content jobs consecutively with customizable intervals between each job. This feature helps you "put out a lot of fresh content" consistently and efficiently.

## New UI Components

### Dashboard Tab - Batch Automation Section

The new **🤖 Batch Automation** section is located below the Job Execution controls on the Dashboard tab and includes:

1. **Status Display**: Shows the current automation state and progress
2. **Jobs to run**: Spinbox to set the number of jobs (1-100)
3. **Interval**: Time to wait between jobs (1-360 minutes/hours)
4. **Force Stuga Brand**: Optional checkbox for brand-specific content
5. **🚀 Start Automated Batch**: But<PERSON> to begin automation
6. **🛑 Stop Automation**: Button to stop the batch (gracefully after current job)

## How It Works

### Job Selection Strategy
The batch automation uses **Option C** from the implementation plan: "Run 'Plan Next Job' then 'Execute' repeatedly"

For each job in the batch:
1. **Planning Phase**: Uses the existing freshness scoring algorithm to select the highest-scoring content that meets the threshold
2. **Execution Phase**: Runs the complete pipeline (SERP analysis, content writing, publishing) using the current Dashboard settings
3. **Wait Phase**: Pauses for the specified interval before the next job (if not the last job)

### Settings Inheritance
Each job in the batch uses the current settings from the Dashboard:
- **Steps**: SERP analysis, Blog writing, Publishing (checkboxes)
- **Data Sources**: SerpApi, AlsoAsked, LLM suggestions (checkboxes)
- **Platform**: Shopify or WordPress
- **Publishing Mode**: Draft or Live
- **LLM Selection**: Provider and model for content generation

## Using the Feature

### Prerequisites
1. **Content Ideas**: Ensure you have content ideas in your database (use the Idea Generator tab)
2. **API Configuration**: Set up your API keys in config.ini for the data sources and LLMs you want to use
3. **Freshness Scores**: Run "Refresh All Scores" to ensure content has up-to-date freshness scores

### Step-by-Step Instructions

1. **Configure Settings**:
   - Set your preferred execution steps (SERP, Write, Publish)
   - Choose data sources (SerpApi, AlsoAsked, LLM)
   - Select publishing platform and draft/live mode
   - Choose your LLM provider and model

2. **Set Batch Parameters**:
   - **Jobs to run**: How many content pieces to process (e.g., 5)
   - **Interval**: Time between jobs (e.g., 10 minutes)
   - This helps avoid rate limits and allows for manual review

3. **Start Automation**:
   - Click **🚀 Start Automated Batch**
   - Monitor progress in the Log tab
   - The status display shows current job progress

4. **Monitor Progress**:
   - Status updates appear in real-time
   - Each job's success/failure is logged
   - Content Plan tab updates after each completion

5. **Stop If Needed**:
   - Click **🛑 Stop Automation** to gracefully stop after the current job
   - The regular **🛑 Stop** button also works for batch operations

## Status Display Messages

- **"Ready for batch automation"** (Blue): System ready to start
- **"Processing job X of Y..."** (Green): Currently executing a job
- **"Waiting X minutes before next job..."** (Orange): In the interval between jobs
- **"Stopping after current job..."** (Red): Stop requested, will finish current job

## Safety Features

### Pause and Cancel
- **Graceful Stop**: The automation will complete the current job before stopping
- **Interruptible Waits**: The interval between jobs can be interrupted by the stop button
- **UI Responsiveness**: The interface remains responsive during batch processing

### Error Handling
- **Job Failures**: If one job fails, the batch continues with the next job
- **No Suitable Content**: If no content meets the freshness threshold, the batch stops
- **API Errors**: Individual step failures are logged, but processing continues

### Rate Limiting
- **Configurable Intervals**: Set appropriate delays between jobs to respect API rate limits
- **Progressive Execution**: Jobs are processed one at a time, not in parallel

## Example Workflow

```
1. Generate 20 content ideas using Idea Generator
2. Run "Refresh All Scores" to calculate freshness scores
3. Configure Dashboard settings:
   - Enable SERP, Write, Publish
   - Use SerpApi + LLM suggestions
   - Publish to Shopify as drafts
   - Use GPT-4 for writing
4. Set batch to run 5 jobs with 15-minute intervals
5. Start automation and monitor progress
6. Review published drafts after completion
```

## Best Practices

### Content Preparation
- Maintain a healthy pipeline of NEW/ON_HOLD content ideas
- Regularly refresh freshness scores to ensure optimal job selection
- Review and adjust freshness threshold in Settings if needed

### Batch Configuration
- **Start Small**: Begin with 2-3 jobs to test your configuration
- **Appropriate Intervals**: Use 10-15 minutes between jobs for API rate limits
- **Monitor First Run**: Watch the first batch closely to ensure proper operation

### Quality Control
- **Draft Mode**: Start with "Publish as Draft" enabled for manual review
- **Content Review**: Review generated content before making it live
- **Settings Optimization**: Adjust LLM and data source settings based on results

## Troubleshooting

### Common Issues

**"No suitable job found"**
- Check freshness threshold in Settings (may be too high)
- Ensure you have NEW/ON_HOLD content with calculated scores
- Run "Refresh All Scores" to update content scoring

**Jobs failing consistently**
- Verify API keys are configured correctly
- Check internet connection and API service status
- Review Log tab for specific error messages

**Slow performance**
- Increase intervals between jobs
- Disable unnecessary data sources
- Check system resources during batch processing

**Automation won't start**
- Ensure no other operations are running
- Check that you have content in the database
- Verify all required settings are configured

### Getting Help

1. **Log Tab**: Check the Log tab for detailed error messages and progress updates
2. **Test Script**: Run `python test_automation.py` to verify component functionality
3. **Manual Testing**: Try running individual jobs manually before attempting batch automation

## Technical Details

### Architecture
- **Threading**: Batch automation runs in a separate thread to keep the UI responsive
- **State Management**: Proper state tracking prevents conflicts with manual operations
- **Database Updates**: Content status and scores are updated after each job
- **GUI Integration**: Real-time updates to all dashboard displays during batch processing

### Database Changes
- No schema changes required
- Uses existing content status workflow (NEW → PLANNED → WRITING → PUBLISHED)
- Integrates with existing freshness scoring system

### Configuration
- Uses existing config.ini settings for API keys and thresholds
- Inherits all execution preferences from the Dashboard tab
- No additional configuration files needed

This feature enables you to scale your content production while maintaining quality and control over the process.
