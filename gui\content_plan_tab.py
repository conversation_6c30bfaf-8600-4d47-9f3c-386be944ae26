"""
Content Plan Tab - Content Strategist Dashboard

This module contains the ContentPlanTab class which handles the content plan
interface including database view, management buttons, search, and content details.
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
from typing import List, Dict, Any, Optional

from gui.gui_utils import decode_freshness_score, safe_sort_key_for_freshness


class ContentPlanTab(ttk.Frame):
    """
    Content Plan tab containing database view and management functionality.
    """

    def __init__(self, master, main_app, db, planner, log_func, *args, **kwargs):
        super().__init__(master, *args, **kwargs)
        
        # Store references to main app components
        self.main_app = main_app
        self.db = db
        self.planner = planner
        self.log_message = log_func
        
        # Initialize variables
        self.sort_column = None
        self.sort_reverse = False
        self.current_content_data = []
        self.next_job_candidate_id = None
        self.selected_content_with_html = None
        
        # Search variable
        self.search_var = tk.StringVar()
        self.search_var.trace('w', self.on_search_changed)
        
        # Create the content plan UI
        self._create_widgets()
        
        # Create context menu
        self.create_context_menu()
        
        # Initial data load
        self.refresh_content_plan()

    def _create_widgets(self):
        """Create the content plan tab widgets."""
        # Configure padding
        self.configure(padding="10")

        # Title and description
        title_label = ttk.Label(self, text="Content Plan",
                               font=("Arial", 16, "bold"))
        title_label.pack(pady=(0, 5))

        desc_label = ttk.Label(self,
                              text="View and manage all content ideas. Green highlight = Next Job Candidate. Right-click for more options.",
                              font=("Arial", 10), foreground="gray")
        desc_label.pack(pady=(0, 15))

        # Search functionality
        search_frame = ttk.Frame(self)
        search_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Label(search_frame, text="Search Content Plan:").pack(side=tk.LEFT, padx=(0, 10))
        search_entry = ttk.Entry(search_frame, textvariable=self.search_var, width=30)
        search_entry.pack(side=tk.LEFT, padx=(0, 10))

        ttk.Button(search_frame, text="Clear Search", command=self.clear_search).pack(side=tk.LEFT, padx=(0, 20))

        # Management buttons (reduced set - main actions moved to context menu)
        ttk.Button(search_frame, text="🔄 Refresh Table", command=self.refresh_content_plan).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(search_frame, text="📊 Auto-Calculate Scores", command=self.auto_calculate_scores).pack(side=tk.LEFT, padx=5)
        ttk.Button(search_frame, text="🔄 Refresh All Scores", command=self.refresh_all_scores).pack(side=tk.LEFT, padx=5)

        # Main content area with resizable panes
        content_paned = ttk.PanedWindow(self, orient=tk.HORIZONTAL)
        content_paned.pack(fill=tk.BOTH, expand=True, pady=(0, 10))

        # TreeView frame
        tree_frame = ttk.Frame(content_paned)
        content_paned.add(tree_frame, weight=3)  # Give tree view more initial space

        # Create TreeView with columns
        columns = ('Status', 'Freshness', 'Keyword', 'Pillar', 'Prompt_Profile', 'Proposed_Angle')
        self.content_tree = ttk.Treeview(tree_frame, columns=columns, show='tree headings')

        # Configure column headings and widths with sorting
        self.content_tree.heading('#0', text='ID ↕', command=lambda: self.sort_content_tree('#0'))
        self.content_tree.column('#0', width=50, minwidth=50)

        for col in columns:
            self.content_tree.heading(col, text=f'{col.replace("_", " ")} ↕', command=lambda c=col: self.sort_content_tree(c))
            if col == 'Keyword':
                self.content_tree.column(col, width=180, minwidth=120)
            elif col == 'Proposed_Angle':
                self.content_tree.column(col, width=250, minwidth=150)
            elif col == 'Prompt_Profile':
                self.content_tree.column(col, width=120, minwidth=80)
            elif col == 'Freshness':
                self.content_tree.column(col, width=80, minwidth=60)
            else:
                self.content_tree.column(col, width=80, minwidth=60)

        # Add scrollbar for TreeView
        tree_scrollbar = ttk.Scrollbar(tree_frame, orient=tk.VERTICAL, command=self.content_tree.yview)
        self.content_tree.configure(yscrollcommand=tree_scrollbar.set)

        # Pack TreeView and scrollbar
        self.content_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        tree_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # Configure tags for visual highlighting
        self.content_tree.tag_configure('next_candidate', background='lightgreen')
        self.content_tree.tag_configure('planned', background='lightblue')
        self.content_tree.tag_configure('high_priority', background='lightyellow')
        self.content_tree.tag_configure('rejected', background='lightcoral')

        # Bind selection event
        self.content_tree.bind('<<TreeviewSelect>>', self.on_content_selection_changed)
        
        # Bind right-click context menu
        self.content_tree.bind('<Button-3>', self.show_context_menu)  # Right-click
        self.content_tree.bind('<Control-Button-1>', self.show_context_menu)  # Ctrl+Click for Mac

        # Detail pane frame
        detail_frame = ttk.LabelFrame(content_paned, text="Content Details", padding="8")
        content_paned.add(detail_frame, weight=1)  # Less initial space for detail pane

        # Detail text area
        detail_text_frame = ttk.Frame(detail_frame)
        detail_text_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 8))

        self.detail_text = tk.Text(detail_text_frame, width=30, wrap=tk.WORD, state=tk.DISABLED)
        detail_scrollbar = ttk.Scrollbar(detail_text_frame, orient=tk.VERTICAL, command=self.detail_text.yview)
        self.detail_text.configure(yscrollcommand=detail_scrollbar.set)

        self.detail_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        detail_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # HTML content action buttons frame (initially hidden)
        self.html_buttons_frame = ttk.Frame(detail_frame)

        self.copy_html_button = ttk.Button(self.html_buttons_frame, text="📋 Copy Full HTML",
                                          command=self.copy_selected_html, state=tk.DISABLED)
        self.copy_html_button.pack(side=tk.LEFT, padx=(0, 5))

        self.save_html_button = ttk.Button(self.html_buttons_frame, text="💾 Save HTML",
                                          command=self.save_selected_html, state=tk.DISABLED)
        self.save_html_button.pack(side=tk.LEFT)

        # Status bar
        self.content_status_var = tk.StringVar(value="Ready")
        ttk.Label(self, textvariable=self.content_status_var).pack(pady=(10, 0))

    def create_context_menu(self):
        """Create the right-click context menu for the content tree."""
        self.context_menu = tk.Menu(self.master, tearoff=0)
        self.context_menu.add_command(label="🎯 Prime This for Execution", command=self.plan_selected_job)
        self.context_menu.add_command(label="⭐ Set High Priority (999)", command=self.prioritize_selected)
        self.context_menu.add_separator()

        # Prompt Profile submenu
        self.profile_submenu = tk.Menu(self.context_menu, tearoff=0)
        self.context_menu.add_cascade(label="📝 Assign Prompt Profile", menu=self.profile_submenu)

        # Toggle Brand Focus
        self.context_menu.add_command(label="✪ Toggle Brand Focus", command=self.toggle_brand_focus)

        # Set Status submenu
        self.status_submenu = tk.Menu(self.context_menu, tearoff=0)
        self.context_menu.add_cascade(label="📊 Set Status...", menu=self.status_submenu)

        # Populate status submenu
        statuses = ['NEW', 'PLANNED', 'WRITING', 'PUBLISHED', 'WRITTEN_NOT_PUBLISHED', 'ON_HOLD', 'REJECTED_USER']
        for status in statuses:
            self.status_submenu.add_command(
                label=status,
                command=lambda s=status: self.set_status_for_selected(s)
            )

        self.context_menu.add_separator()
        self.context_menu.add_command(label="📝 Set Custom Score...", command=self.set_custom_score)
        self.context_menu.add_command(label="🔄 Move to Top", command=self.move_to_top)
        self.context_menu.add_command(label="⬇️ Move to Bottom", command=self.move_to_bottom)
        self.context_menu.add_separator()
        self.context_menu.add_command(label="❌ Veto/Reject", command=self.veto_selected)
        self.context_menu.add_command(label="🗑️ Delete", command=self.delete_selected)
        self.context_menu.add_separator()
        self.context_menu.add_command(label="📋 Copy Keyword", command=self.copy_keyword)
        self.context_menu.add_command(label="📄 View Details", command=self.view_details)

    def show_context_menu(self, event):
        """Show the context menu at the cursor position."""
        try:
            # Select the item under the cursor
            item = self.content_tree.identify_row(event.y)
            if item:
                self.content_tree.selection_set(item)
                self.content_tree.focus(item)

                # Populate the prompt profile submenu
                self.populate_profile_submenu()

                # Show the context menu
                self.context_menu.post(event.x_root, event.y_root)
        except Exception as e:
            self.log_message(f"Error showing context menu: {e}", "error")

    def populate_profile_submenu(self):
        """Populate the prompt profile submenu with available profiles."""
        try:
            # Clear existing submenu items
            self.profile_submenu.delete(0, tk.END)

            # Get all prompt profiles
            all_profiles = self.db.get_all_prompt_profiles()

            # Add profile options
            for profile in all_profiles:
                profile_name = profile['profile_name']
                if profile.get('is_default'):
                    profile_name += " (Default)"

                self.profile_submenu.add_command(
                    label=profile_name,
                    command=lambda p_id=profile['id']: self.assign_prompt_profile_to_selected(p_id)
                )

            # Add separator and "Revert to Default" option
            if all_profiles:
                self.profile_submenu.add_separator()

            self.profile_submenu.add_command(
                label="Revert to Default",
                command=lambda: self.assign_prompt_profile_to_selected(None)
            )

        except Exception as e:
            self.log_message(f"Error populating profile submenu: {e}", "error")

    def assign_prompt_profile_to_selected(self, profile_id):
        """Assign a prompt profile to the selected content item(s)."""
        try:
            selected_items = self.content_tree.selection()
            if not selected_items:
                messagebox.showwarning("No Selection", "Please select content item(s) to assign a profile.")
                return

            # Get profile name for logging
            if profile_id:
                profile = self.db.get_prompt_profile(profile_id)
                profile_name = profile['profile_name'] if profile else f"Profile ID {profile_id}"
            else:
                profile_name = "Default"

            # Update each selected item
            updated_count = 0
            for item in selected_items:
                content_id = int(self.content_tree.item(item)['text'])
                success = self.db.update_content_prompt_profile(content_id, profile_id)
                if success:
                    updated_count += 1

            if updated_count > 0:
                self.log_message(f"✅ Assigned '{profile_name}' profile to {updated_count} content item(s)", "success")

                # Refresh the content plan view
                self.refresh_content_plan()
            else:
                self.log_message("❌ Failed to assign profile to any content items", "error")

        except Exception as e:
            self.log_message(f"Error assigning prompt profile: {e}", "error")

    def on_search_changed(self, *args):
        """Handle search text changes."""
        try:
            search_term = self.search_var.get().strip()
            if search_term:
                # Search in database
                filtered_content = self.db.search_content(search_term)
                self.populate_content_tree(filtered_content)
            else:
                # Show all content
                self.refresh_content_plan()
        except Exception as e:
            self.log_message(f"Error during search: {e}", "error")

    def clear_search(self):
        """Clear the search field and show all content."""
        self.search_var.set("")
        self.refresh_content_plan()

    def get_selected_content_id(self):
        """Get the ID of the currently selected content item."""
        selected = self.content_tree.selection()
        if not selected:
            return None
        item = self.content_tree.item(selected[0])
        return int(item['text'])

    # Delegate methods to main app for functionality that requires cross-tab coordination
    def auto_calculate_scores(self):
        """Delegate to main app."""
        self.main_app.auto_calculate_scores()

    def refresh_all_scores(self):
        """Delegate to main app."""
        self.main_app.refresh_all_scores()

    def open_cleanup_dialog(self):
        """Delegate to main app."""
        self.main_app.open_cleanup_dialog()

    def open_dangerous_operations_dialog(self):
        """Delegate to main app."""
        self.main_app.open_dangerous_operations_dialog()

    def plan_selected_job(self):
        """Delegate to main app."""
        self.main_app.plan_selected_job()

    def set_custom_score(self):
        """Delegate to main app."""
        self.main_app.set_custom_score()

    def move_to_top(self):
        """Delegate to main app."""
        self.main_app.move_to_top()

    def move_to_bottom(self):
        """Delegate to main app."""
        self.main_app.move_to_bottom()

    def copy_keyword(self):
        """Delegate to main app."""
        self.main_app.copy_keyword()

    def view_details(self):
        """Delegate to main app."""
        self.main_app.view_details()

    def sort_content_tree(self, column):
        """Sort the content tree by the specified column."""
        try:
            # Toggle sort direction if same column
            if self.sort_column == column:
                self.sort_reverse = not self.sort_reverse
            else:
                self.sort_column = column
                self.sort_reverse = False

            # Sort the data
            if column == '#0':  # ID column
                sorted_data = sorted(self.current_content_data,
                                   key=lambda x: x.get('id', 0),
                                   reverse=self.sort_reverse)
            elif column == 'Freshness':
                sorted_data = sorted(self.current_content_data,
                                   key=safe_sort_key_for_freshness,
                                   reverse=self.sort_reverse)
            else:
                # For other columns, sort by string value
                sorted_data = sorted(self.current_content_data,
                                   key=lambda x: str(x.get(column.lower(), '')),
                                   reverse=self.sort_reverse)

            # Update the tree view
            self.populate_content_tree(sorted_data)

        except Exception as e:
            self.log_message(f"Error sorting content tree: {e}", "error")

    def on_content_selection_changed(self, event):
        """Handle content selection change to update detail view."""
        try:
            selection = self.content_tree.selection()
            if not selection:
                # Clear detail view and hide HTML buttons
                self.detail_text.config(state=tk.NORMAL)
                self.detail_text.delete('1.0', tk.END)
                self.detail_text.insert('1.0', "Select a content idea to view details.")
                self.detail_text.config(state=tk.DISABLED)

                # Hide HTML buttons and clear reference
                self.html_buttons_frame.pack_forget()
                self.copy_html_button.config(state=tk.DISABLED)
                self.save_html_button.config(state=tk.DISABLED)
                self.selected_content_with_html = None
                return

            # Get selected content details
            item = self.content_tree.item(selection[0])
            content_id = int(item['text'])
            content = self.db.get_content_by_id(content_id)

            if content:
                # Build detail text
                detail_text = f"Content ID: {content['id']}\n"
                detail_text += f"Keyword: {content['keyword']}\n"
                detail_text += f"Pillar: {content['pillar']}\n"
                detail_text += f"Craft: {content['craft']}\n"
                detail_text += f"Status: {content['status']}\n"

                # Decode and display freshness score
                freshness_score = decode_freshness_score(content.get('freshness_score'), content['keyword'])
                detail_text += f"Freshness Score: {freshness_score:.2f}\n"

                detail_text += f"Created: {content.get('created_date', 'N/A')}\n"
                detail_text += f"Updated: {content.get('updated_date', 'N/A')}\n\n"

                if content.get('proposed_angle'):
                    detail_text += f"Proposed Angle:\n{content['proposed_angle']}\n\n"

                if content.get('platform_url'):
                    detail_text += f"Published URL: {content['platform_url']}\n"
                    detail_text += f"Published Date: {content.get('published_date', 'N/A')}\n\n"

                # Check for generated content
                if content.get('generated_content'):
                    detail_text += f"Generated Content: {len(content['generated_content'])} characters\n"
                    # Show first 200 characters as preview
                    preview = content['generated_content'][:200]
                    if len(content['generated_content']) > 200:
                        preview += "..."
                    detail_text += f"Preview: {preview}\n\n"

                # Check for HTML content
                if content.get('html_content'):
                    detail_text += f"HTML Content: {len(content['html_content'])} characters\n"
                    detail_text += "Use buttons below to copy or save full HTML content.\n\n"

                    # Show HTML buttons and store reference
                    self.html_buttons_frame.pack(pady=(8, 0))
                    self.copy_html_button.config(state=tk.NORMAL)
                    self.save_html_button.config(state=tk.NORMAL)
                    self.selected_content_with_html = content
                else:
                    # Hide HTML buttons and clear reference
                    self.html_buttons_frame.pack_forget()
                    self.copy_html_button.config(state=tk.DISABLED)
                    self.save_html_button.config(state=tk.DISABLED)
                    self.selected_content_with_html = None

                # Update detail view
                self.detail_text.config(state=tk.NORMAL)
                self.detail_text.delete('1.0', tk.END)
                self.detail_text.insert('1.0', detail_text)
                self.detail_text.config(state=tk.DISABLED)

        except Exception as e:
            self.log_message(f"Error updating detail view: {e}", "error")

    def copy_selected_html(self):
        """Copy the selected content's HTML to clipboard."""
        try:
            if not self.selected_content_with_html:
                messagebox.showwarning("No Content", "No content with HTML is currently selected.")
                return

            html_content = self.selected_content_with_html.get('html_content', '')
            if html_content:
                # Copy to clipboard
                self.master.clipboard_clear()
                self.master.clipboard_append(html_content)

                keyword = self.selected_content_with_html.get('keyword', 'Unknown')
                self.log_message(f"📋 Copied HTML content for '{keyword}' to clipboard", "success")
                messagebox.showinfo("Copied", f"HTML content for '{keyword}' copied to clipboard!")
            else:
                messagebox.showwarning("No HTML", "Selected content has no HTML content.")

        except Exception as e:
            self.log_message(f"Error copying HTML content: {e}", "error")
            messagebox.showerror("Error", f"Failed to copy HTML content: {e}")

    def save_selected_html(self):
        """Save the selected content's HTML to a file."""
        try:
            if not self.selected_content_with_html:
                messagebox.showwarning("No Content", "No content with HTML is currently selected.")
                return

            html_content = self.selected_content_with_html.get('html_content', '')
            if not html_content:
                messagebox.showwarning("No HTML", "Selected content has no HTML content.")
                return

            # Get keyword for default filename
            keyword = self.selected_content_with_html.get('keyword', 'content')
            safe_keyword = "".join(c for c in keyword if c.isalnum() or c in (' ', '-', '_')).rstrip()
            default_filename = f"{safe_keyword}_content.html"

            # Ask user where to save
            filename = filedialog.asksaveasfilename(
                defaultextension=".html",
                filetypes=[("HTML files", "*.html"), ("All files", "*.*")],
                title="Save HTML Content",
                initialvalue=default_filename
            )

            if filename:
                # Save HTML content
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(html_content)

                self.log_message(f"💾 Saved HTML content for '{keyword}' to {filename}", "success")
                messagebox.showinfo("Saved", f"HTML content saved to:\n{filename}")

        except Exception as e:
            self.log_message(f"Error saving HTML content: {e}", "error")
            messagebox.showerror("Error", f"Failed to save HTML content: {e}")

    def populate_content_tree(self, content_data):
        """Populate the content tree with the given data."""
        try:
            # Store current data for sorting
            self.current_content_data = content_data

            # Clear existing items
            for item in self.content_tree.get_children():
                self.content_tree.delete(item)

            # Add content to tree
            for content in content_data:
                content_id = content['id']
                status = content.get('status', 'UNKNOWN')

                # Decode freshness score safely
                freshness_score = decode_freshness_score(content.get('freshness_score'), content.get('keyword', 'Unknown'))
                freshness_display = f"{freshness_score:.1f}"

                keyword = content.get('keyword', 'N/A')
                # Add brand focus indicator if is_brand_centric is true
                if content.get('is_brand_centric', False):
                    keyword = f"✪ {keyword}"

                pillar = content.get('pillar', 'N/A')
                proposed_angle = content.get('proposed_angle', 'N/A')

                # Get prompt profile name
                prompt_profile_display = "Default"
                prompt_profile_id = content.get('prompt_profile_id')
                if prompt_profile_id:
                    try:
                        profile = self.db.get_prompt_profile(prompt_profile_id)
                        if profile:
                            prompt_profile_display = profile['profile_name']
                        else:
                            prompt_profile_display = f"Unknown (ID: {prompt_profile_id})"
                    except Exception:
                        prompt_profile_display = f"Error (ID: {prompt_profile_id})"

                # Truncate long angles for display
                if len(proposed_angle) > 50:
                    proposed_angle = proposed_angle[:47] + "..."

                values = (status, freshness_display, keyword, pillar, prompt_profile_display, proposed_angle)

                # Determine tag for highlighting
                tags = []
                if content_id == self.main_app.next_job_candidate_id:
                    tags.append('next_candidate')
                elif status == 'PLANNED':
                    tags.append('planned')
                elif status == 'REJECTED':
                    tags.append('rejected')
                elif freshness_score >= 999:
                    tags.append('high_priority')

                self.content_tree.insert('', 'end', text=str(content_id), values=values, tags=tags)

            # Update status
            self.content_status_var.set(f"Showing {len(content_data)} content ideas")

        except Exception as e:
            self.log_message(f"Error populating content tree: {e}", "error")
            import traceback
            print(f"Full traceback: {traceback.format_exc()}")

    def refresh_content_plan(self):
        """Refresh the content plan TreeView."""
        try:
            # Get all content
            all_content = self.db.get_all_content()

            # Update next job candidate ID from main app
            if hasattr(self.main_app, 'next_job_candidate_id'):
                self.next_job_candidate_id = self.main_app.next_job_candidate_id

            # Populate tree
            self.populate_content_tree(all_content)

            # Update status
            self.content_status_var.set(f"Content plan refreshed - {len(all_content)} items")

        except Exception as e:
            self.log_message(f"Error refreshing content plan: {e}", "error")
            import traceback
            print(f"Full traceback: {traceback.format_exc()}")

    def prioritize_selected(self):
        """Set the selected content idea to highest priority."""
        selected = self.content_tree.selection()
        if not selected:
            messagebox.showwarning("No Selection", "Please select a content idea to prioritize.")
            return

        try:
            item = self.content_tree.item(selected[0])
            content_id = int(item['text'])

            # Set to highest priority score
            self.db.update_freshness_score(content_id, 999.0)

            keyword = item['values'][2]  # Keyword is at index 2
            self.log_message(f"⭐ Prioritized content: '{keyword}' (ID: {content_id})", "success")

            # Update displays
            self.main_app.update_next_job_info()
            self.refresh_content_plan()
            self.main_app.update_dashboard_status()

        except Exception as e:
            self.log_message(f"Error prioritizing content: {e}", "error")

    def veto_selected(self):
        """Mark the selected content idea as rejected."""
        selected = self.content_tree.selection()
        if not selected:
            messagebox.showwarning("No Selection", "Please select a content idea to veto.")
            return

        try:
            item = self.content_tree.item(selected[0])
            content_id = int(item['text'])
            keyword = item['values'][2]  # Keyword is at index 2

            # Confirm action
            if messagebox.askyesno("Confirm Veto", f"Mark '{keyword}' as rejected?\n\nThis will remove it from consideration for future jobs."):
                self.db.update_content_status(content_id, 'REJECTED_USER')
                self.log_message(f"❌ Vetoed content: '{keyword}' (ID: {content_id})", "warning")

                # Update displays
                self.main_app.update_next_job_info()
                self.refresh_content_plan()
                self.main_app.update_dashboard_status()

        except Exception as e:
            self.log_message(f"Error vetoing content: {e}", "error")

    def delete_selected(self):
        """Delete the selected content idea from the database."""
        selected = self.content_tree.selection()
        if not selected:
            messagebox.showwarning("No Selection", "Please select a content idea to delete.")
            return

        try:
            item = self.content_tree.item(selected[0])
            content_id = int(item['text'])
            keyword = item['values'][2]  # Keyword is at index 2

            # Confirm deletion
            if messagebox.askyesno("Confirm Deletion", f"Permanently delete '{keyword}'?\n\nThis action cannot be undone."):
                self.db.delete_content(content_id)
                self.log_message(f"🗑️ Deleted content: '{keyword}' (ID: {content_id})", "warning")

                # Update displays
                self.main_app.update_next_job_info()
                self.refresh_content_plan()
                self.main_app.update_dashboard_status()

        except Exception as e:
            self.log_message(f"Error deleting content: {e}", "error")

    def toggle_brand_focus(self):
        """Toggle the brand-centric status for selected content item(s)."""
        try:
            selected_items = self.content_tree.selection()
            if not selected_items:
                messagebox.showwarning("No Selection", "Please select content item(s) to toggle brand focus.")
                return

            # Delegate to main app
            self.main_app.toggle_selected_brand_focus()

        except Exception as e:
            self.log_message(f"Error toggling brand focus: {e}", "error")

    def set_status_for_selected(self, new_status):
        """Set the status for selected content item(s)."""
        try:
            selected_items = self.content_tree.selection()
            if not selected_items:
                messagebox.showwarning("No Selection", "Please select content item(s) to set status.")
                return

            # Delegate to main app
            self.main_app.set_selected_status_from_plan_tab(new_status)

        except Exception as e:
            self.log_message(f"Error setting status: {e}", "error")

    def update_database_instance(self, new_db, new_planner):
        """Update database and backend component references."""
        self.db = new_db
        self.planner = new_planner

    def reset_for_new_database(self):
        """Reset tab state for new database."""
        # Clear current data
        self.current_content_data = []
        self.next_job_candidate_id = None

        # Clear detail text
        self.detail_text.config(state=tk.NORMAL)
        self.detail_text.delete('1.0', tk.END)
        self.detail_text.config(state=tk.DISABLED)

        # Refresh content plan
        self.refresh_content_plan()
