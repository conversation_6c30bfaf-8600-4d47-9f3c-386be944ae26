import sqlite3
import os
import json
from datetime import datetime
from typing import List, Dict, Any, Optional, Tuple
import pickle

# Database file path
DB_FILE = os.path.join(os.path.dirname(__file__), 'content_ledger.db')

class ContentDatabase:
    """
    Database manager for the Content Strategist system.
    Handles all CRUD operations for the content_ledger table.
    """
    
    def __init__(self, db_path: str = None):
        """Initialize the database connection and create tables if needed."""
        self.db_path = db_path or DB_FILE
        self.init_database()
    
    def init_database(self):
        """Create the database and tables if they don't exist."""
        # Ensure the directory exists
        import os
        db_dir = os.path.dirname(self.db_path)
        if db_dir and not os.path.exists(db_dir):
            os.makedirs(db_dir, exist_ok=True)

        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()

            # Create the prompt_profiles table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS prompt_profiles (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    profile_name TEXT UNIQUE NOT NULL,
                    prompt_text TEXT NOT NULL,
                    is_default BOOLEAN DEFAULT 0,
                    created_date DATETIME DEFAULT CURRENT_TIMESTAMP,
                    updated_date DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            ''')

            # Create the content_ledger table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS content_ledger (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    keyword TEXT NOT NULL,
                    pillar TEXT NOT NULL,
                    craft TEXT NOT NULL,
                    status TEXT NOT NULL DEFAULT 'NEW',
                    proposed_angle TEXT,
                    freshness_score REAL DEFAULT 0.0,
                    platform_url TEXT,
                    published_date DATETIME,
                    keyword_vector BLOB,
                    generated_content_html TEXT DEFAULT NULL,
                    prompt_profile_id INTEGER NULL REFERENCES prompt_profiles(id) ON DELETE SET NULL,
                    created_date DATETIME DEFAULT CURRENT_TIMESTAMP,
                    updated_date DATETIME DEFAULT CURRENT_TIMESTAMP,
                    CONSTRAINT valid_status CHECK (status IN ('NEW', 'PLANNED', 'WRITING', 'PUBLISHED', 'WRITTEN_NOT_PUBLISHED', 'ON_HOLD', 'REJECTED_USER'))
                )
            ''')

            # Create basic indexes for better performance
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_status ON content_ledger(status)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_freshness_score ON content_ledger(freshness_score)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_pillar ON content_ledger(pillar)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_craft ON content_ledger(craft)')

            # Database migration logic (this will create additional indexes as needed)
            self._migrate_database(cursor)

            conn.commit()
            print(f"Database initialized at: {self.db_path}")

    def _migrate_database(self, cursor):
        """Handle database migrations for schema changes."""
        # Check current table structure
        cursor.execute("PRAGMA table_info(content_ledger)")
        columns = [column[1] for column in cursor.fetchall()]

        # Migration 1: Add generated_content_html column if missing
        if 'generated_content_html' not in columns:
            print("Migrating database: Adding generated_content_html column...")
            cursor.execute('ALTER TABLE content_ledger ADD COLUMN generated_content_html TEXT DEFAULT NULL')
            print("✅ Database migration completed for generated_content_html")

        # Migration 2: Add is_brand_centric column if missing (for backward compatibility)
        if 'is_brand_centric' not in columns:
            print("Migrating database: Adding is_brand_centric column...")
            cursor.execute('ALTER TABLE content_ledger ADD COLUMN is_brand_centric BOOLEAN DEFAULT 0')
            print("✅ Database migration completed for is_brand_centric")

        # Migration 3: Add prompt_profile_id column if missing
        if 'prompt_profile_id' not in columns:
            print("Migrating database: Adding prompt_profile_id column...")
            cursor.execute('ALTER TABLE content_ledger ADD COLUMN prompt_profile_id INTEGER NULL REFERENCES prompt_profiles(id) ON DELETE SET NULL')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_prompt_profile_id ON content_ledger(prompt_profile_id)')
            print("✅ Database migration completed for prompt_profile_id")

            # Migrate existing is_brand_centric data to prompt profiles
            self._migrate_brand_centric_to_profiles(cursor)

    def _migrate_brand_centric_to_profiles(self, cursor):
        """Migrate existing is_brand_centric data to prompt profiles."""
        try:
            # Check if we have any brand-centric content
            cursor.execute('SELECT COUNT(*) FROM content_ledger WHERE is_brand_centric = 1')
            brand_centric_count = cursor.fetchone()[0]

            if brand_centric_count > 0:
                print(f"Found {brand_centric_count} brand-centric content items to migrate...")

                # Create default brand-centric profile if it doesn't exist
                cursor.execute('SELECT id FROM prompt_profiles WHERE profile_name = ?', ('Brand-Centric Default',))
                profile = cursor.fetchone()

                if not profile:
                    # Create the brand-centric profile with a basic prompt
                    brand_prompt = """Write engaging, informative content that naturally showcases our brand's products and values.
Focus on authentic storytelling that connects with our audience while providing genuine value.
Integrate relevant products naturally when they add value to the content.

Available placeholders:
- {keyword}: The target keyword
- {analysis_results}: SERP analysis insights
- {word_count}: Target word count
- {stuga_brand_and_product_context}: Product information
- {gatekeeper_feedback}: Editorial feedback"""

                    cursor.execute('''
                        INSERT INTO prompt_profiles (profile_name, prompt_text, is_default)
                        VALUES (?, ?, 0)
                    ''', ('Brand-Centric Default', brand_prompt))

                    brand_profile_id = cursor.lastrowid
                    print("✅ Created 'Brand-Centric Default' prompt profile")
                else:
                    brand_profile_id = profile[0]

                # Update all brand-centric content to use the new profile
                cursor.execute('''
                    UPDATE content_ledger
                    SET prompt_profile_id = ?
                    WHERE is_brand_centric = 1
                ''', (brand_profile_id,))

                updated_count = cursor.rowcount
                print(f"✅ Migrated {updated_count} content items to use Brand-Centric Default profile")

        except Exception as e:
            print(f"Warning: Error during brand-centric migration: {e}")
            # Continue with initialization even if migration fails
    
    def insert_content_idea(self, keyword: str, pillar: str, craft: str,
                           proposed_angle: str = None, keyword_vector: bytes = None,
                           is_brand_centric: bool = False, prompt_profile_id: Optional[int] = None) -> int:
        """
        Insert a new content idea into the database.

        Args:
            keyword: The target keyword
            pillar: The business pillar this content belongs to
            craft: The craft/category this content belongs to
            proposed_angle: The proposed content angle/approach
            keyword_vector: Serialized vector embedding of the keyword
            is_brand_centric: Whether this content should use brand-centric writing style (deprecated, use prompt_profile_id)
            prompt_profile_id: ID of the prompt profile to use for this content

        Returns:
            The ID of the newly inserted record
        """
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()

            # Handle backward compatibility: if is_brand_centric is True and no prompt_profile_id specified,
            # try to find the Brand-Centric Default profile
            if is_brand_centric and prompt_profile_id is None:
                cursor.execute('SELECT id FROM prompt_profiles WHERE profile_name = ?', ('Brand-Centric Default',))
                profile = cursor.fetchone()
                if profile:
                    prompt_profile_id = profile[0]

            cursor.execute('''
                INSERT INTO content_ledger
                (keyword, pillar, craft, proposed_angle, keyword_vector, is_brand_centric, prompt_profile_id, status)
                VALUES (?, ?, ?, ?, ?, ?, ?, 'NEW')
            ''', (keyword, pillar, craft, proposed_angle, keyword_vector, is_brand_centric, prompt_profile_id))

            content_id = cursor.lastrowid
            conn.commit()

            profile_info = f"Profile ID={prompt_profile_id}" if prompt_profile_id else "No profile"
            print(f"Inserted new content idea: ID={content_id}, Keyword='{keyword}', Pillar='{pillar}', {profile_info}")
            return content_id
    
    def get_content_by_status(self, status: str) -> List[Dict[str, Any]]:
        """
        Retrieve all content records with a specific status.
        
        Args:
            status: The status to filter by ('NEW', 'PLANNED', 'WRITING', etc.)
            
        Returns:
            List of dictionaries representing the records
        """
        with sqlite3.connect(self.db_path) as conn:
            conn.row_factory = sqlite3.Row  # Enable dict-like access
            cursor = conn.cursor()
            cursor.execute('''
                SELECT * FROM content_ledger 
                WHERE status = ? 
                ORDER BY freshness_score DESC, created_date ASC
            ''', (status,))
            
            return [dict(row) for row in cursor.fetchall()]
    
    def get_content_by_statuses(self, statuses: List[str]) -> List[Dict[str, Any]]:
        """
        Retrieve all content records with any of the specified statuses.
        
        Args:
            statuses: List of statuses to filter by
            
        Returns:
            List of dictionaries representing the records
        """
        placeholders = ','.join(['?' for _ in statuses])
        with sqlite3.connect(self.db_path) as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            cursor.execute(f'''
                SELECT * FROM content_ledger 
                WHERE status IN ({placeholders})
                ORDER BY freshness_score DESC, created_date ASC
            ''', statuses)
            
            return [dict(row) for row in cursor.fetchall()]
    
    def update_content_status(self, content_id: int, new_status: str) -> bool:
        """
        Update the status of a content record.
        
        Args:
            content_id: The ID of the content record
            new_status: The new status to set
            
        Returns:
            True if the update was successful, False otherwise
        """
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                UPDATE content_ledger 
                SET status = ?, updated_date = CURRENT_TIMESTAMP
                WHERE id = ?
            ''', (new_status, content_id))
            
            success = cursor.rowcount > 0
            conn.commit()
            
            if success:
                print(f"Updated content ID={content_id} status to '{new_status}'")
            else:
                print(f"Failed to update content ID={content_id} - record not found")
            
            return success
    
    def update_freshness_score(self, content_id: int, score: float) -> bool:
        """
        Update the freshness score of a content record.
        Enhanced with comprehensive byte detection and conversion.
        
        Args:
            content_id: The ID of the content record
            score: The new freshness score
            
        Returns:
            True if the update was successful, False otherwise
        """
        # Debug: Check what type of score we're receiving
        print(f"🔍 DB UPDATE DEBUG: Received score {repr(score)} (type: {type(score)}) for content_id {content_id}")
        
        # Ensure score is a proper float with comprehensive handling
        try:
            if isinstance(score, bytes):
                print(f"⚠️ DB UPDATE DEBUG: Score is bytes! Converting...")
                import struct
                if len(score) == 4:
                    # Decode 32-bit IEEE 754 float
                    score = struct.unpack('f', score)[0]
                    print(f"✅ DB UPDATE DEBUG: Converted 32-bit bytes to float: {score}")
                elif len(score) == 8:
                    # Handle potential 64-bit double
                    score = struct.unpack('d', score)[0]
                    print(f"✅ DB UPDATE DEBUG: Converted 64-bit bytes to float: {score}")
                else:
                    print(f"❌ DB UPDATE DEBUG: Invalid bytes length {len(score)}, using 0.0")
                    score = 0.0
            elif score is None:
                print(f"⚠️ DB UPDATE DEBUG: Score is None, using 0.0")
                score = 0.0
            else:
                # Ensure it's a proper float
                original_score = score
                score = float(score)
                print(f"✅ DB UPDATE DEBUG: Score converted to float: {original_score} -> {score}")
                
            # Additional validation
            if not isinstance(score, (int, float)) or str(score).lower() in ('nan', 'inf', '-inf'):
                print(f"❌ DB UPDATE DEBUG: Invalid score value {score}, using 0.0")
                score = 0.0
                
        except (ValueError, TypeError, struct.error) as e:
            print(f"❌ DB UPDATE DEBUG: Error converting score: {e}, using 0.0")
            score = 0.0
        
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                UPDATE content_ledger 
                SET freshness_score = ?, updated_date = CURRENT_TIMESTAMP
                WHERE id = ?
            ''', (score, content_id))
            
            success = cursor.rowcount > 0
            conn.commit()
            
            if success:
                print(f"Updated content ID={content_id} freshness score to {score}")
                
                # Debug: Verify what was actually stored
                cursor.execute('SELECT freshness_score FROM content_ledger WHERE id = ?', (content_id,))
                stored_score = cursor.fetchone()
                if stored_score:
                    print(f"🔍 DB VERIFY DEBUG: Actually stored: {repr(stored_score[0])} (type: {type(stored_score[0])})")
            
            return success
    
    def update_generated_content(self, content_id: int, content_html: str) -> bool:
        """
        Update the generated content HTML for a content record.
        
        Args:
            content_id: The ID of the content record
            content_html: The generated HTML content
            
        Returns:
            True if the update was successful, False otherwise
        """
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                UPDATE content_ledger 
                SET generated_content_html = ?, updated_date = CURRENT_TIMESTAMP
                WHERE id = ?
            ''', (content_html, content_id))
            
            success = cursor.rowcount > 0
            conn.commit()
            
            if success:
                print(f"Updated content ID={content_id} with generated HTML content")
            
            return success
    
    def update_content_published(self, content_id: int, platform_url: str) -> bool:
        """
        Mark content as published with the platform URL.
        
        Args:
            content_id: The ID of the content record
            platform_url: The URL where the content was published
            
        Returns:
            True if the update was successful, False otherwise
        """
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                UPDATE content_ledger 
                SET status = 'PUBLISHED', platform_url = ?, 
                    published_date = CURRENT_TIMESTAMP, updated_date = CURRENT_TIMESTAMP
                WHERE id = ?
            ''', (platform_url, content_id))
            
            success = cursor.rowcount > 0
            conn.commit()
            
            if success:
                print(f"Marked content ID={content_id} as published at {platform_url}")
            
            return success
    
    def get_content_by_id(self, content_id: int) -> Optional[Dict[str, Any]]:
        """
        Retrieve a specific content record by ID.
        
        Args:
            content_id: The ID of the content record
            
        Returns:
            Dictionary representing the record, or None if not found
        """
        with sqlite3.connect(self.db_path) as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            cursor.execute('SELECT * FROM content_ledger WHERE id = ?', (content_id,))
            
            row = cursor.fetchone()
            return dict(row) if row else None
    
    def get_all_content(self) -> List[Dict[str, Any]]:
        """
        Retrieve all content records.

        Returns:
            List of dictionaries representing all records
        """
        with sqlite3.connect(self.db_path) as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            cursor.execute('''
                SELECT * FROM content_ledger
                ORDER BY freshness_score DESC, created_date DESC
            ''')

            return [dict(row) for row in cursor.fetchall()]

    def get_content_by_id(self, content_id: int) -> Optional[Dict[str, Any]]:
        """
        Retrieve a specific content record by ID.

        Args:
            content_id: The ID of the content record to retrieve

        Returns:
            Dictionary representing the record or None if not found
        """
        with sqlite3.connect(self.db_path) as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            cursor.execute('SELECT * FROM content_ledger WHERE id = ?', (content_id,))
            row = cursor.fetchone()
            return dict(row) if row else None
    
    def delete_content(self, content_id: int) -> bool:
        """
        Delete a content record.
        
        Args:
            content_id: The ID of the content record to delete
            
        Returns:
            True if the deletion was successful, False otherwise
        """
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('DELETE FROM content_ledger WHERE id = ?', (content_id,))
            
            success = cursor.rowcount > 0
            conn.commit()
            
            if success:
                print(f"Deleted content ID={content_id}")
            else:
                print(f"Failed to delete content ID={content_id} - record not found")
            
            return success
    
    def get_content_stats(self) -> Dict[str, int]:
        """
        Get statistics about content in the database.
        
        Returns:
            Dictionary with counts by status
        """
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                SELECT status, COUNT(*) as count 
                FROM content_ledger 
                GROUP BY status
            ''')
            
            stats = {}
            for row in cursor.fetchall():
                stats[row[0]] = row[1]
            
            return stats
    
    def search_content(self, search_term: str) -> List[Dict[str, Any]]:
        """
        Search for content by keyword, pillar, or proposed angle.
        
        Args:
            search_term: The term to search for
            
        Returns:
            List of matching content records
        """
        with sqlite3.connect(self.db_path) as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            search_pattern = f'%{search_term}%'
            cursor.execute('''
                SELECT * FROM content_ledger 
                WHERE keyword LIKE ? OR pillar LIKE ? OR proposed_angle LIKE ?
                ORDER BY freshness_score DESC, created_date DESC
            ''', (search_pattern, search_pattern, search_pattern))
            
            return [dict(row) for row in cursor.fetchall()]
    
    def get_content_by_keyword(self, keyword: str) -> Optional[Dict[str, Any]]:
        """Get content by exact keyword match.
        
        Args:
            keyword: The exact keyword to search for
            
        Returns:
            Dictionary representing the content record, or None if not found
        """
        with sqlite3.connect(self.db_path) as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            cursor.execute('SELECT * FROM content_ledger WHERE keyword = ?', (keyword,))
            
            row = cursor.fetchone()
            return dict(row) if row else None
    
    def add_content_idea(self, keyword: str, pillar: str, craft: str,
                        proposed_angle: str = None, status: str = 'NEW', is_brand_centric: bool = False) -> int:
        """Add a content idea with specified status (wrapper for insert_content_idea).

        Args:
            keyword: The target keyword
            pillar: The business pillar this content belongs to
            craft: The craft/category this content belongs to
            proposed_angle: The proposed content angle/approach
            status: The initial status (default: 'NEW')
            is_brand_centric: Whether this content should use brand-centric writing style

        Returns:
            The ID of the newly inserted record
        """
        return self.insert_content_idea(keyword, pillar, craft, proposed_angle, is_brand_centric=is_brand_centric)

    # Prompt Profile Management Methods

    def add_prompt_profile(self, name: str, text: str, is_default: bool = False) -> int:
        """
        Add a new prompt profile to the database.

        Args:
            name: Unique name for the prompt profile
            text: The prompt template text
            is_default: Whether this should be the default profile

        Returns:
            The ID of the newly created profile
        """
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()

            # If setting as default, unset other defaults first
            if is_default:
                cursor.execute('UPDATE prompt_profiles SET is_default = 0')

            cursor.execute('''
                INSERT INTO prompt_profiles (profile_name, prompt_text, is_default)
                VALUES (?, ?, ?)
            ''', (name, text, is_default))

            profile_id = cursor.lastrowid
            conn.commit()

            default_info = " (set as default)" if is_default else ""
            print(f"Created prompt profile: ID={profile_id}, Name='{name}'{default_info}")
            return profile_id

    def get_prompt_profile(self, profile_id: int) -> Optional[Dict[str, Any]]:
        """
        Get a prompt profile by ID.

        Args:
            profile_id: The ID of the profile to retrieve

        Returns:
            Dictionary containing profile data or None if not found
        """
        with sqlite3.connect(self.db_path) as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            cursor.execute('SELECT * FROM prompt_profiles WHERE id = ?', (profile_id,))
            row = cursor.fetchone()
            return dict(row) if row else None

    def get_prompt_profile_by_name(self, name: str) -> Optional[Dict[str, Any]]:
        """
        Get a prompt profile by name.

        Args:
            name: The name of the profile to retrieve

        Returns:
            Dictionary containing profile data or None if not found
        """
        with sqlite3.connect(self.db_path) as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            cursor.execute('SELECT * FROM prompt_profiles WHERE profile_name = ?', (name,))
            row = cursor.fetchone()
            return dict(row) if row else None

    def get_all_prompt_profiles(self) -> List[Dict[str, Any]]:
        """
        Get all prompt profiles.

        Returns:
            List of dictionaries containing all profile data
        """
        with sqlite3.connect(self.db_path) as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            cursor.execute('SELECT * FROM prompt_profiles ORDER BY profile_name')
            return [dict(row) for row in cursor.fetchall()]

    def update_prompt_profile(self, profile_id: int, name: str = None, text: str = None, is_default: bool = None) -> bool:
        """
        Update a prompt profile.

        Args:
            profile_id: The ID of the profile to update
            name: New name for the profile (optional)
            text: New prompt text (optional)
            is_default: New default status (optional)

        Returns:
            True if the update was successful, False otherwise
        """
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()

            # Build update query dynamically based on provided parameters
            updates = []
            params = []

            if name is not None:
                updates.append('profile_name = ?')
                params.append(name)

            if text is not None:
                updates.append('prompt_text = ?')
                params.append(text)

            if is_default is not None:
                # If setting as default, unset other defaults first
                if is_default:
                    cursor.execute('UPDATE prompt_profiles SET is_default = 0')
                updates.append('is_default = ?')
                params.append(is_default)

            if not updates:
                return False  # Nothing to update

            updates.append('updated_date = CURRENT_TIMESTAMP')
            params.append(profile_id)

            query = f"UPDATE prompt_profiles SET {', '.join(updates)} WHERE id = ?"
            cursor.execute(query, params)

            success = cursor.rowcount > 0
            conn.commit()

            if success:
                print(f"Updated prompt profile ID={profile_id}")

            return success

    def delete_prompt_profile(self, profile_id: int) -> bool:
        """
        Delete a prompt profile.

        Args:
            profile_id: The ID of the profile to delete

        Returns:
            True if the deletion was successful, False otherwise
        """
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()

            # Check if this is the default profile
            cursor.execute('SELECT is_default, profile_name FROM prompt_profiles WHERE id = ?', (profile_id,))
            profile_info = cursor.fetchone()

            if not profile_info:
                return False  # Profile doesn't exist

            is_default, profile_name = profile_info

            # Delete the profile (content_ledger entries will have prompt_profile_id set to NULL due to ON DELETE SET NULL)
            cursor.execute('DELETE FROM prompt_profiles WHERE id = ?', (profile_id,))
            success = cursor.rowcount > 0

            if success and is_default:
                # If we deleted the default profile, try to set another as default
                cursor.execute('SELECT id FROM prompt_profiles ORDER BY created_date LIMIT 1')
                next_profile = cursor.fetchone()
                if next_profile:
                    cursor.execute('UPDATE prompt_profiles SET is_default = 1 WHERE id = ?', (next_profile[0],))
                    print(f"Set profile ID={next_profile[0]} as new default after deleting default profile")

            conn.commit()

            if success:
                print(f"Deleted prompt profile: '{profile_name}' (ID={profile_id})")

            return success

    def set_default_prompt_profile(self, profile_id: int) -> bool:
        """
        Set a prompt profile as the default.

        Args:
            profile_id: The ID of the profile to set as default

        Returns:
            True if successful, False otherwise
        """
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()

            # Check if profile exists
            cursor.execute('SELECT profile_name FROM prompt_profiles WHERE id = ?', (profile_id,))
            profile = cursor.fetchone()

            if not profile:
                return False

            # Unset all defaults
            cursor.execute('UPDATE prompt_profiles SET is_default = 0')

            # Set the specified profile as default
            cursor.execute('UPDATE prompt_profiles SET is_default = 1, updated_date = CURRENT_TIMESTAMP WHERE id = ?', (profile_id,))

            success = cursor.rowcount > 0
            conn.commit()

            if success:
                print(f"Set '{profile[0]}' as default prompt profile")

            return success

    def get_default_prompt_profile(self) -> Optional[Dict[str, Any]]:
        """
        Get the default prompt profile.

        Returns:
            Dictionary containing the default profile data or None if no default is set
        """
        with sqlite3.connect(self.db_path) as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            cursor.execute('SELECT * FROM prompt_profiles WHERE is_default = 1 LIMIT 1')
            row = cursor.fetchone()
            return dict(row) if row else None

    def update_content_prompt_profile(self, content_id: int, profile_id: Optional[int]) -> bool:
        """
        Update the prompt profile for a content item.

        Args:
            content_id: The ID of the content item
            profile_id: The ID of the prompt profile to assign (None to revert to default)

        Returns:
            True if successful, False otherwise
        """
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                UPDATE content_ledger
                SET prompt_profile_id = ?, updated_date = CURRENT_TIMESTAMP
                WHERE id = ?
            ''', (profile_id, content_id))

            success = cursor.rowcount > 0
            conn.commit()

            if success:
                profile_info = f"Profile ID={profile_id}" if profile_id else "Default profile"
                print(f"Updated content ID={content_id} to use {profile_info}")

            return success

    def update_brand_centric_status(self, content_id: int, is_brand_centric: bool) -> bool:
        """
        Update the brand-centric status for a content item.

        Args:
            content_id: The ID of the content item
            is_brand_centric: Whether this content should use brand-centric writing style

        Returns:
            True if successful, False otherwise
        """
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                UPDATE content_ledger
                SET is_brand_centric = ?, updated_date = CURRENT_TIMESTAMP
                WHERE id = ?
            ''', (is_brand_centric, content_id))

            success = cursor.rowcount > 0
            conn.commit()

            if success:
                status = "enabled" if is_brand_centric else "disabled"
                print(f"Updated content ID={content_id} brand-centric status to {status}")

            return success


# Convenience functions for common operations
def get_database() -> ContentDatabase:
    """Get a database instance."""
    return ContentDatabase()

def serialize_vector(vector) -> bytes:
    """Serialize a vector for storage in the database."""
    return pickle.dumps(vector)

def deserialize_vector(vector_bytes: bytes):
    """Deserialize a vector from the database."""
    return pickle.loads(vector_bytes) if vector_bytes else None


# Example usage and testing
if __name__ == '__main__':
    print("Testing Content Database...")
    
    # Initialize database
    db = ContentDatabase()
    
    # Test inserting content ideas
    test_ideas = [
        ("best shaving brush", "grooming", "shaving", "Comprehensive guide to choosing quality shaving brushes"),
        ("natural beard oil", "grooming", "beard care", "Benefits of natural ingredients in beard care"),
        ("leather wallet care", "leather goods", "accessories", "How to maintain and care for leather wallets"),
        ("sustainable grooming", "sustainability", "eco-friendly", "Eco-friendly alternatives in men's grooming")
    ]
    
    for keyword, pillar, craft, angle in test_ideas:
        db.insert_content_idea(keyword, pillar, craft, angle)
    
    # Test retrieving content
    print("\n--- All Content ---")
    all_content = db.get_all_content()
    for content in all_content:
        print(f"ID: {content['id']}, Keyword: {content['keyword']}, Status: {content['status']}")
    
    # Test status updates
    if all_content:
        first_id = all_content[0]['id']
        db.update_freshness_score(first_id, 85.5)
        db.update_content_status(first_id, 'PLANNED')
    
    # Test statistics
    print("\n--- Content Statistics ---")
    stats = db.get_content_stats()
    for status, count in stats.items():
        print(f"{status}: {count}")
    
    print("\nDatabase testing complete!")
