"""
Content Strategist - Main Entry Point

This is the main entry point for the Content Strategist application.
It initializes the database and launches the new GUI interface.
"""

import tkinter as tk
import sys
import os
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from gui.main_window import MainWindow
from core import config_manager

def main():
    """Main entry point for the Content Strategist application."""
    try:
        # Initialize the database
        print("Initializing Content Strategist...")

        # Get the initial database path from configuration
        initial_db_path = config_manager.get_active_db_path()
        print(f"Using database: {initial_db_path}")

        # Ensure the directory for initial_db_path exists
        db_dir = os.path.dirname(initial_db_path)
        if db_dir and not os.path.exists(db_dir):
            os.makedirs(db_dir, exist_ok=True)
            print(f"Created database directory: {db_dir}")

        print("Database path configured successfully!")

        # Create and run the GUI
        root = tk.Tk()
        MainWindow(root, initial_db_path=initial_db_path)

        print("Content Strategist GUI launched!")
        root.mainloop()

    except ImportError as e:
        print(f"Import error: {e}")
        print("Please make sure all required packages are installed:")
        print("pip install -r requirements.txt")
        sys.exit(1)
    except Exception as e:
        print(f"Error starting Content Strategist: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
