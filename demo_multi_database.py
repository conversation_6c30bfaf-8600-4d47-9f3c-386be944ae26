#!/usr/bin/env python3
"""
Multi-Database Feature Demo

This script demonstrates the multi-database functionality that has been implemented.
It shows how to:
1. Create and manage multiple database configurations
2. Switch between databases
3. Verify data isolation
"""

import sys
import os
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from core import config_manager
from database import ContentDatabase


def demo_configuration_management():
    """Demonstrate configuration management for multiple databases."""
    print("🎯 Multi-Database Configuration Management Demo")
    print("=" * 50)
    
    # Show current configuration
    print("1. Current database configuration:")
    databases = config_manager.get_databases_config()
    for key, info in databases.items():
        print(f"   {key}: {info['name']} -> {info['path']}")
    
    active_path = config_manager.get_active_db_path()
    print(f"   Active database: {active_path}")
    
    # Add a new database configuration
    print("\n2. Adding a new database configuration...")
    demo_name = "Demo Project Database"
    demo_path = "demo_project.db"
    demo_key = config_manager.add_database_entry(demo_name, demo_path)
    print(f"   Added: {demo_key} -> {demo_name}")
    
    # Show updated configuration
    print("\n3. Updated configuration:")
    databases = config_manager.get_databases_config()
    for key, info in databases.items():
        print(f"   {key}: {info['name']} -> {info['path']}")
    
    # Switch to the new database
    print(f"\n4. Switching to new database...")
    config_manager.set_active_db_key(demo_key)
    new_active_path = config_manager.get_active_db_path()
    print(f"   New active database: {new_active_path}")
    
    # Clean up
    print(f"\n5. Cleaning up demo configuration...")
    config_manager.remove_database_entry(demo_key)
    config_manager.set_active_db_key('db1')
    print("   Demo configuration removed, reset to default")


def demo_data_isolation():
    """Demonstrate data isolation between different databases."""
    print("\n🎯 Data Isolation Demo")
    print("=" * 30)
    
    # Create two separate database instances
    db1_path = "demo_db1.db"
    db2_path = "demo_db2.db"
    
    print(f"1. Creating two separate databases:")
    print(f"   DB1: {db1_path}")
    print(f"   DB2: {db2_path}")
    
    db1 = ContentDatabase(db_path=db1_path)
    db2 = ContentDatabase(db_path=db2_path)
    
    # Add different content to each database
    print("\n2. Adding different content to each database:")
    db1_id = db1.add_content_idea("shaving brush guide", "Grooming", "Shaving", "Complete guide to badger brushes")
    db2_id = db2.add_content_idea("leather wallet care", "Leather Goods", "Accessories", "How to maintain leather wallets")
    
    print(f"   DB1 content ID: {db1_id}")
    print(f"   DB2 content ID: {db2_id}")
    
    # Verify isolation
    print("\n3. Verifying data isolation:")
    db1_content = db1.get_all_content()
    db2_content = db2.get_all_content()
    
    print(f"   DB1 has {len(db1_content)} items: {[c['keyword'] for c in db1_content]}")
    print(f"   DB2 has {len(db2_content)} items: {[c['keyword'] for c in db2_content]}")
    
    # Cross-check
    db1_leather = db1.get_content_by_keyword("leather wallet care")
    db2_shaving = db2.get_content_by_keyword("shaving brush guide")
    
    print(f"   DB1 can see 'leather wallet care': {db1_leather is not None}")
    print(f"   DB2 can see 'shaving brush guide': {db2_shaving is not None}")
    
    if db1_leather is None and db2_shaving is None:
        print("   ✅ Perfect isolation - each database only sees its own content!")
    else:
        print("   ❌ Data leakage detected!")


def demo_backend_components():
    """Demonstrate backend component initialization with custom databases."""
    print("\n🎯 Backend Component Integration Demo")
    print("=" * 40)
    
    # Create a custom database
    custom_db_path = "demo_backend.db"
    print(f"1. Creating custom database: {custom_db_path}")
    custom_db = ContentDatabase(db_path=custom_db_path)
    
    # Add some test content
    custom_db.add_content_idea("test keyword", "test pillar", "test craft", "test angle")
    
    try:
        # Import and initialize backend components
        from planner import ContentPlanner
        from worker import ContentWorker
        from idea_generator import IdeaGenerator
        
        print("\n2. Initializing backend components with custom database:")
        
        # Initialize with custom database
        planner = ContentPlanner(db_instance=custom_db)
        worker = ContentWorker(db_instance=custom_db)
        idea_gen = IdeaGenerator(db_instance=custom_db)
        
        print(f"   Planner database: {planner.db.db_path}")
        print(f"   Worker database: {worker.db.db_path}")
        print(f"   IdeaGenerator database: {idea_gen.db.db_path}")
        
        # Verify they can access the custom database content
        content = planner.db.get_all_content()
        print(f"   Backend components can see {len(content)} items in custom database")
        
        print("   ✅ Backend components successfully initialized with custom database!")
        
    except ImportError as e:
        print(f"   ⚠️ Could not test backend components: {e}")


def demo_gui_integration():
    """Show how the GUI integration would work."""
    print("\n🎯 GUI Integration Overview")
    print("=" * 30)
    
    print("The GUI now includes:")
    print("• File menu with 'Switch/Load Database...' and 'Create New Database...' options")
    print("• Database Switcher Dialog for managing multiple databases")
    print("• Automatic re-initialization when switching databases")
    print("• Tab-level reset methods to clear state for new databases")
    
    print("\nTo use the multi-database feature:")
    print("1. Launch the application: python main_gui.py")
    print("2. Go to File > Switch/Load Database... to see available databases")
    print("3. Use 'Add New Database...' to create additional project databases")
    print("4. Switch between databases as needed for different projects")
    print("5. Each database maintains completely separate content and settings")


def main():
    """Run the multi-database feature demonstration."""
    print("🚀 Multi-Database Feature Demonstration")
    print("This demo shows the new multi-database functionality")
    print("that allows users to manage separate content databases")
    print("for different projects or businesses.\n")
    
    try:
        demo_configuration_management()
        demo_data_isolation()
        demo_backend_components()
        demo_gui_integration()
        
        print("\n" + "=" * 60)
        print("🎉 Multi-Database Feature Demo Complete!")
        print("\nKey Benefits:")
        print("• Complete data isolation between projects")
        print("• Easy switching through GUI interface")
        print("• Automatic component re-initialization")
        print("• Persistent configuration management")
        print("• Backward compatibility with existing databases")
        
        print("\nThe multi-database feature is ready for production use!")
        
        # Clean up demo files
        print("\n🧹 Cleaning up demo files...")
        for demo_file in ["demo_project.db", "demo_db1.db", "demo_db2.db", "demo_backend.db"]:
            if os.path.exists(demo_file):
                try:
                    os.remove(demo_file)
                    print(f"   Removed: {demo_file}")
                except Exception:
                    print(f"   Could not remove: {demo_file} (may be in use)")
        
        return True
        
    except Exception as e:
        print(f"\n❌ Demo failed: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
