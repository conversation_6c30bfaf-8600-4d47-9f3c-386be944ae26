2025-06-23 20:13:30 - __main__ - INFO - Starting Shopify SEO Image Automation Tool
2025-06-23 20:13:30 - __main__ - INFO - Initializing settings with automatic config detection...
2025-06-23 20:13:30 - config.settings - INFO - Found config file at primary location: c:\Users\<USER>\Documents\Code\Shopify Image Renamer\config\config.json
2025-06-23 20:13:30 - config.settings - INFO - Settings loaded successfully
2025-06-23 20:13:30 - __main__ - INFO - Config loaded from: c:\Users\<USER>\Documents\Code\Shopify Image Renamer\config\config.json
2025-06-23 20:13:30 - __main__ - INFO - Config status: Found (16 settings)
2025-06-23 20:13:30 - gui.main_window - INFO - Loaded 42 stored models for openai
2025-06-23 20:13:30 - config.settings - INFO - Settings saved successfully
2025-06-23 20:13:30 - config.settings - INFO - Settings saved successfully
2025-06-23 20:13:30 - config.settings - INFO - Settings saved successfully
2025-06-23 20:13:30 - gui.main_window - INFO - Settings loaded - provider: openai
2025-06-23 20:13:47 - config.settings - INFO - Settings saved successfully
2025-06-23 20:13:47 - gui.main_window - INFO - Added 7 images to processing queue
2025-06-23 20:14:07 - config.settings - INFO - Settings saved successfully
2025-06-23 20:14:07 - config.settings - INFO - Settings saved successfully
2025-06-23 20:14:07 - config.settings - INFO - Settings saved successfully
2025-06-23 20:14:07 - config.settings - INFO - Settings saved successfully
2025-06-23 20:14:07 - gui.main_window - INFO - Auto-run mode: All workflow steps enabled
2025-06-23 20:14:08 - gui.main_window - INFO - Starting processing with workflow: AI Analysis → File Rename → Shopify Upload
2025-06-23 20:14:08 - gui.main_window - INFO - === PROCESSING WORKFLOW ENABLED ===
2025-06-23 20:14:08 - gui.main_window - INFO - Workflow steps: AI Analysis → File Rename → Shopify Upload
2025-06-23 20:14:08 - gui.main_window - INFO - AI Analysis: ✅ ENABLED
2025-06-23 20:14:08 - gui.main_window - INFO - File Rename: ✅ ENABLED
2025-06-23 20:14:08 - gui.main_window - INFO - Shopify Upload: ✅ ENABLED
2025-06-23 20:14:08 - gui.main_window - INFO - ===================================
2025-06-23 20:14:08 - gui.main_window - INFO - Starting processing of 7 images
2025-06-23 20:14:08 - gui.main_window - INFO - 
📁 Processing image 1/7: DSC08139_resized.webp
2025-06-23 20:14:08 - gui.main_window - INFO - 📄 Original file path: C:/Users/<USER>/Pictures/23-06-25/edited/processed_resized\DSC08139_resized.webp
2025-06-23 20:14:08 - gui.main_window - INFO - Step 1/3: Starting AI analysis for DSC08139_resized.webp
2025-06-23 20:14:10 - gui.main_window - INFO - AI analysis complete for DSC08139_resized.webp: silicone-pet-food-bowls
2025-06-23 20:14:10 - gui.main_window - INFO - Step 2/3: File rename requested for DSC08139_resized.webp
2025-06-23 20:14:10 - gui.main_window - INFO - Renaming DSC08139_resized.webp → silicone-pet-food-bowls
2025-06-23 20:14:10 - utils.file_manager - INFO - Renamed C:/Users/<USER>/Pictures/23-06-25/edited/processed_resized\DSC08139_resized.webp to C:/Users/<USER>/Pictures/23-06-25/edited/processed_resized\silicone-pet-food-bowls.webp
2025-06-23 20:14:10 - gui.main_window - INFO - Successfully renamed: DSC08139_resized.webp → silicone-pet-food-bowls.webp
2025-06-23 20:14:10 - gui.main_window - INFO - Step 3/3: Starting Shopify upload for silicone-pet-food-bowls.webp
2025-06-23 20:14:10 - gui.main_window - INFO - 📤 Final file path to upload: C:/Users/<USER>/Pictures/23-06-25/edited/processed_resized\silicone-pet-food-bowls.webp
2025-06-23 20:14:10 - gui.main_window - INFO - Uploading to Shopify: silicone-pet-food-bowls.webp (alt: 'Set of three round silicone pet food bowls in oliv...')
2025-06-23 20:14:10 - shopify.client - INFO - Starting GraphQL file upload for silicone-pet-food-bowls.webp (size: 18330 bytes)
2025-06-23 20:14:10 - shopify.client - INFO - Creating staged upload for silicone-pet-food-bowls.webp
2025-06-23 20:14:11 - shopify.client - INFO - Staged upload created successfully for silicone-pet-food-bowls.webp
2025-06-23 20:14:11 - shopify.client - INFO - Uploading silicone-pet-food-bowls.webp to staged URL
2025-06-23 20:14:11 - shopify.client - INFO - File silicone-pet-food-bowls.webp uploaded to staged URL successfully
2025-06-23 20:14:11 - shopify.client - INFO - Creating file from staged upload
2025-06-23 20:14:12 - shopify.client - INFO - FileCreate response status: 200
2025-06-23 20:14:12 - shopify.client - INFO - FileCreate response data: {'data': {'fileCreate': {'files': [{'id': 'gid://shopify/MediaImage/37311006048487', 'fileStatus': 'UPLOADED', 'alt': 'Set of three round silicone pet food bowls in olive green, black, and white colors for feeding pets', 'createdAt': '2025-06-23T10:14:11Z', 'image': None}], 'userErrors': []}}, 'extensions': {'cost': {'requestedQueryCost': 21, 'actualQueryCost': 21, 'throttleStatus': {'maximumAvailable': 2000.0, 'currentlyAvailable': 1979, 'restoreRate': 100.0}}}}
2025-06-23 20:14:12 - shopify.client - INFO - Extracting file info from: {'id': 'gid://shopify/MediaImage/37311006048487', 'fileStatus': 'UPLOADED', 'alt': 'Set of three round silicone pet food bowls in olive green, black, and white colors for feeding pets', 'createdAt': '2025-06-23T10:14:11Z', 'image': None}
2025-06-23 20:14:12 - shopify.client - INFO - Base file info extracted: {'id': 'gid://shopify/MediaImage/37311006048487', 'alt': 'Set of three round silicone pet food bowls in olive green, black, and white colors for feeding pets', 'fileStatus': 'UPLOADED', 'createdAt': '2025-06-23T10:14:11Z'}
2025-06-23 20:14:12 - shopify.client - INFO - Image data not yet available (file still processing). Status: UPLOADED
2025-06-23 20:14:12 - shopify.client - INFO - File created successfully in Shopify with ID: gid://shopify/MediaImage/37311006048487
2025-06-23 20:14:12 - shopify.client - INFO - Successfully uploaded silicone-pet-food-bowls.webp to Shopify via GraphQL
2025-06-23 20:14:12 - gui.main_window - INFO - Successfully uploaded silicone-pet-food-bowls.webp to Shopify (ID: gid://shopify/MediaImage/37311006048487)
2025-06-23 20:14:12 - gui.main_window - INFO - 📝 Note: Image data will be available after Shopify processing completes
2025-06-23 20:14:12 - gui.main_window - INFO - 
📁 Processing image 2/7: DSC08140_resized.webp
2025-06-23 20:14:12 - gui.main_window - INFO - 📄 Original file path: C:/Users/<USER>/Pictures/23-06-25/edited/processed_resized\DSC08140_resized.webp
2025-06-23 20:14:12 - gui.main_window - INFO - Step 1/3: Starting AI analysis for DSC08140_resized.webp
2025-06-23 20:14:14 - gui.main_window - INFO - AI analysis complete for DSC08140_resized.webp: olive-green-round-dog-bowl
2025-06-23 20:14:14 - gui.main_window - INFO - Step 2/3: File rename requested for DSC08140_resized.webp
2025-06-23 20:14:14 - gui.main_window - INFO - Renaming DSC08140_resized.webp → olive-green-round-dog-bowl
2025-06-23 20:14:14 - utils.file_manager - INFO - Renamed C:/Users/<USER>/Pictures/23-06-25/edited/processed_resized\DSC08140_resized.webp to C:/Users/<USER>/Pictures/23-06-25/edited/processed_resized\olive-green-round-dog-bowl.webp
2025-06-23 20:14:14 - gui.main_window - INFO - Successfully renamed: DSC08140_resized.webp → olive-green-round-dog-bowl.webp
2025-06-23 20:14:14 - gui.main_window - INFO - Step 3/3: Starting Shopify upload for olive-green-round-dog-bowl.webp
2025-06-23 20:14:14 - gui.main_window - INFO - 📤 Final file path to upload: C:/Users/<USER>/Pictures/23-06-25/edited/processed_resized\olive-green-round-dog-bowl.webp
2025-06-23 20:14:14 - gui.main_window - INFO - Uploading to Shopify: olive-green-round-dog-bowl.webp (alt: 'Olive green round dog bowl made of durable materia...')
2025-06-23 20:14:14 - shopify.client - INFO - Starting GraphQL file upload for olive-green-round-dog-bowl.webp (size: 38426 bytes)
2025-06-23 20:14:14 - shopify.client - INFO - Creating staged upload for olive-green-round-dog-bowl.webp
2025-06-23 20:14:15 - shopify.client - INFO - Staged upload created successfully for olive-green-round-dog-bowl.webp
2025-06-23 20:14:15 - shopify.client - INFO - Uploading olive-green-round-dog-bowl.webp to staged URL
2025-06-23 20:14:15 - shopify.client - INFO - File olive-green-round-dog-bowl.webp uploaded to staged URL successfully
2025-06-23 20:14:15 - shopify.client - INFO - Creating file from staged upload
2025-06-23 20:14:16 - shopify.client - INFO - FileCreate response status: 200
2025-06-23 20:14:16 - shopify.client - INFO - FileCreate response data: {'data': {'fileCreate': {'files': [{'id': 'gid://shopify/MediaImage/37311006081255', 'fileStatus': 'UPLOADED', 'alt': 'Olive green round dog bowl made of durable material with smooth finish for pet feeding', 'createdAt': '2025-06-23T10:14:15Z', 'image': None}], 'userErrors': []}}, 'extensions': {'cost': {'requestedQueryCost': 21, 'actualQueryCost': 21, 'throttleStatus': {'maximumAvailable': 2000.0, 'currentlyAvailable': 1979, 'restoreRate': 100.0}}}}
2025-06-23 20:14:16 - shopify.client - INFO - Extracting file info from: {'id': 'gid://shopify/MediaImage/37311006081255', 'fileStatus': 'UPLOADED', 'alt': 'Olive green round dog bowl made of durable material with smooth finish for pet feeding', 'createdAt': '2025-06-23T10:14:15Z', 'image': None}
2025-06-23 20:14:16 - shopify.client - INFO - Base file info extracted: {'id': 'gid://shopify/MediaImage/37311006081255', 'alt': 'Olive green round dog bowl made of durable material with smooth finish for pet feeding', 'fileStatus': 'UPLOADED', 'createdAt': '2025-06-23T10:14:15Z'}
2025-06-23 20:14:16 - shopify.client - INFO - Image data not yet available (file still processing). Status: UPLOADED
2025-06-23 20:14:16 - shopify.client - INFO - File created successfully in Shopify with ID: gid://shopify/MediaImage/37311006081255
2025-06-23 20:14:16 - shopify.client - INFO - Successfully uploaded olive-green-round-dog-bowl.webp to Shopify via GraphQL
2025-06-23 20:14:16 - gui.main_window - INFO - Successfully uploaded olive-green-round-dog-bowl.webp to Shopify (ID: gid://shopify/MediaImage/37311006081255)
2025-06-23 20:14:16 - gui.main_window - INFO - 📝 Note: Image data will be available after Shopify processing completes
2025-06-23 20:14:16 - gui.main_window - INFO - 
📁 Processing image 3/7: DSC08145_resized.webp
2025-06-23 20:14:16 - gui.main_window - INFO - 📄 Original file path: C:/Users/<USER>/Pictures/23-06-25/edited/processed_resized\DSC08145_resized.webp
2025-06-23 20:14:16 - gui.main_window - INFO - Step 1/3: Starting AI analysis for DSC08145_resized.webp
2025-06-23 20:14:18 - gui.main_window - INFO - AI analysis complete for DSC08145_resized.webp: green-stainless-steel-dog-bowl
2025-06-23 20:14:18 - gui.main_window - INFO - Step 2/3: File rename requested for DSC08145_resized.webp
2025-06-23 20:14:18 - gui.main_window - INFO - Renaming DSC08145_resized.webp → green-stainless-steel-dog-bowl
2025-06-23 20:14:18 - utils.file_manager - INFO - Renamed C:/Users/<USER>/Pictures/23-06-25/edited/processed_resized\DSC08145_resized.webp to C:/Users/<USER>/Pictures/23-06-25/edited/processed_resized\green-stainless-steel-dog-bowl.webp
2025-06-23 20:14:18 - gui.main_window - INFO - Successfully renamed: DSC08145_resized.webp → green-stainless-steel-dog-bowl.webp
2025-06-23 20:14:18 - gui.main_window - INFO - Step 3/3: Starting Shopify upload for green-stainless-steel-dog-bowl.webp
2025-06-23 20:14:18 - gui.main_window - INFO - 📤 Final file path to upload: C:/Users/<USER>/Pictures/23-06-25/edited/processed_resized\green-stainless-steel-dog-bowl.webp
2025-06-23 20:14:18 - gui.main_window - INFO - Uploading to Shopify: green-stainless-steel-dog-bowl.webp (alt: 'Green stainless steel dog bowl with non-slip base ...')
2025-06-23 20:14:18 - shopify.client - INFO - Starting GraphQL file upload for green-stainless-steel-dog-bowl.webp (size: 26154 bytes)
2025-06-23 20:14:18 - shopify.client - INFO - Creating staged upload for green-stainless-steel-dog-bowl.webp
2025-06-23 20:14:18 - shopify.client - INFO - Staged upload created successfully for green-stainless-steel-dog-bowl.webp
2025-06-23 20:14:18 - shopify.client - INFO - Uploading green-stainless-steel-dog-bowl.webp to staged URL
2025-06-23 20:14:19 - shopify.client - INFO - File green-stainless-steel-dog-bowl.webp uploaded to staged URL successfully
2025-06-23 20:14:19 - shopify.client - INFO - Creating file from staged upload
2025-06-23 20:14:19 - shopify.client - INFO - FileCreate response status: 200
2025-06-23 20:14:19 - shopify.client - INFO - FileCreate response data: {'data': {'fileCreate': {'files': [{'id': 'gid://shopify/MediaImage/37311006179559', 'fileStatus': 'UPLOADED', 'alt': 'Green stainless steel dog bowl with non-slip base for pet feeding', 'createdAt': '2025-06-23T10:14:19Z', 'image': None}], 'userErrors': []}}, 'extensions': {'cost': {'requestedQueryCost': 21, 'actualQueryCost': 21, 'throttleStatus': {'maximumAvailable': 2000.0, 'currentlyAvailable': 1979, 'restoreRate': 100.0}}}}
2025-06-23 20:14:19 - shopify.client - INFO - Extracting file info from: {'id': 'gid://shopify/MediaImage/37311006179559', 'fileStatus': 'UPLOADED', 'alt': 'Green stainless steel dog bowl with non-slip base for pet feeding', 'createdAt': '2025-06-23T10:14:19Z', 'image': None}
2025-06-23 20:14:19 - shopify.client - INFO - Base file info extracted: {'id': 'gid://shopify/MediaImage/37311006179559', 'alt': 'Green stainless steel dog bowl with non-slip base for pet feeding', 'fileStatus': 'UPLOADED', 'createdAt': '2025-06-23T10:14:19Z'}
2025-06-23 20:14:19 - shopify.client - INFO - Image data not yet available (file still processing). Status: UPLOADED
2025-06-23 20:14:19 - shopify.client - INFO - File created successfully in Shopify with ID: gid://shopify/MediaImage/37311006179559
2025-06-23 20:14:19 - shopify.client - INFO - Successfully uploaded green-stainless-steel-dog-bowl.webp to Shopify via GraphQL
2025-06-23 20:14:19 - gui.main_window - INFO - Successfully uploaded green-stainless-steel-dog-bowl.webp to Shopify (ID: gid://shopify/MediaImage/37311006179559)
2025-06-23 20:14:19 - gui.main_window - INFO - 📝 Note: Image data will be available after Shopify processing completes
2025-06-23 20:14:19 - gui.main_window - INFO - 
📁 Processing image 4/7: DSC08149_resized.webp
2025-06-23 20:14:19 - gui.main_window - INFO - 📄 Original file path: C:/Users/<USER>/Pictures/23-06-25/edited/processed_resized\DSC08149_resized.webp
2025-06-23 20:14:19 - gui.main_window - INFO - Step 1/3: Starting AI analysis for DSC08149_resized.webp
2025-06-23 20:14:21 - gui.main_window - INFO - AI analysis complete for DSC08149_resized.webp: green-travel-soap-container
2025-06-23 20:14:21 - gui.main_window - INFO - Step 2/3: File rename requested for DSC08149_resized.webp
2025-06-23 20:14:21 - gui.main_window - INFO - Renaming DSC08149_resized.webp → green-travel-soap-container
2025-06-23 20:14:21 - utils.file_manager - INFO - Renamed C:/Users/<USER>/Pictures/23-06-25/edited/processed_resized\DSC08149_resized.webp to C:/Users/<USER>/Pictures/23-06-25/edited/processed_resized\green-travel-soap-container.webp
2025-06-23 20:14:21 - gui.main_window - INFO - Successfully renamed: DSC08149_resized.webp → green-travel-soap-container.webp
2025-06-23 20:14:21 - gui.main_window - INFO - Step 3/3: Starting Shopify upload for green-travel-soap-container.webp
2025-06-23 20:14:21 - gui.main_window - INFO - 📤 Final file path to upload: C:/Users/<USER>/Pictures/23-06-25/edited/processed_resized\green-travel-soap-container.webp
2025-06-23 20:14:21 - gui.main_window - INFO - Uploading to Shopify: green-travel-soap-container.webp (alt: 'Hand holding a green round travel soap container w...')
2025-06-23 20:14:21 - shopify.client - INFO - Starting GraphQL file upload for green-travel-soap-container.webp (size: 32516 bytes)
2025-06-23 20:14:21 - shopify.client - INFO - Creating staged upload for green-travel-soap-container.webp
2025-06-23 20:14:22 - shopify.client - INFO - Staged upload created successfully for green-travel-soap-container.webp
2025-06-23 20:14:22 - shopify.client - INFO - Uploading green-travel-soap-container.webp to staged URL
2025-06-23 20:14:22 - shopify.client - INFO - File green-travel-soap-container.webp uploaded to staged URL successfully
2025-06-23 20:14:22 - shopify.client - INFO - Creating file from staged upload
2025-06-23 20:14:23 - shopify.client - INFO - FileCreate response status: 200
2025-06-23 20:14:23 - shopify.client - INFO - FileCreate response data: {'data': {'fileCreate': {'files': [{'id': 'gid://shopify/MediaImage/37311006671079', 'fileStatus': 'UPLOADED', 'alt': 'Hand holding a green round travel soap container with a white soap bar inside, ideal for camping and outdoor use', 'createdAt': '2025-06-23T10:14:22Z', 'image': None}], 'userErrors': []}}, 'extensions': {'cost': {'requestedQueryCost': 21, 'actualQueryCost': 21, 'throttleStatus': {'maximumAvailable': 2000.0, 'currentlyAvailable': 1979, 'restoreRate': 100.0}}}}
2025-06-23 20:14:23 - shopify.client - INFO - Extracting file info from: {'id': 'gid://shopify/MediaImage/37311006671079', 'fileStatus': 'UPLOADED', 'alt': 'Hand holding a green round travel soap container with a white soap bar inside, ideal for camping and outdoor use', 'createdAt': '2025-06-23T10:14:22Z', 'image': None}
2025-06-23 20:14:23 - shopify.client - INFO - Base file info extracted: {'id': 'gid://shopify/MediaImage/37311006671079', 'alt': 'Hand holding a green round travel soap container with a white soap bar inside, ideal for camping and outdoor use', 'fileStatus': 'UPLOADED', 'createdAt': '2025-06-23T10:14:22Z'}
2025-06-23 20:14:23 - shopify.client - INFO - Image data not yet available (file still processing). Status: UPLOADED
2025-06-23 20:14:23 - shopify.client - INFO - File created successfully in Shopify with ID: gid://shopify/MediaImage/37311006671079
2025-06-23 20:14:23 - shopify.client - INFO - Successfully uploaded green-travel-soap-container.webp to Shopify via GraphQL
2025-06-23 20:14:23 - gui.main_window - INFO - Successfully uploaded green-travel-soap-container.webp to Shopify (ID: gid://shopify/MediaImage/37311006671079)
2025-06-23 20:14:23 - gui.main_window - INFO - 📝 Note: Image data will be available after Shopify processing completes
2025-06-23 20:14:23 - gui.main_window - INFO - 
📁 Processing image 5/7: DSC08150_resized.webp
2025-06-23 20:14:23 - gui.main_window - INFO - 📄 Original file path: C:/Users/<USER>/Pictures/23-06-25/edited/processed_resized\DSC08150_resized.webp
2025-06-23 20:14:23 - gui.main_window - INFO - Step 1/3: Starting AI analysis for DSC08150_resized.webp
2025-06-23 20:14:25 - gui.main_window - INFO - AI analysis complete for DSC08150_resized.webp: wood-handle-shaving-brush-stand
2025-06-23 20:14:25 - gui.main_window - INFO - Step 2/3: File rename requested for DSC08150_resized.webp
2025-06-23 20:14:25 - gui.main_window - INFO - Renaming DSC08150_resized.webp → wood-handle-shaving-brush-stand
2025-06-23 20:14:25 - utils.file_manager - INFO - Renamed C:/Users/<USER>/Pictures/23-06-25/edited/processed_resized\DSC08150_resized.webp to C:/Users/<USER>/Pictures/23-06-25/edited/processed_resized\wood-handle-shaving-brush-stand.webp
2025-06-23 20:14:25 - gui.main_window - INFO - Successfully renamed: DSC08150_resized.webp → wood-handle-shaving-brush-stand.webp
2025-06-23 20:14:25 - gui.main_window - INFO - Step 3/3: Starting Shopify upload for wood-handle-shaving-brush-stand.webp
2025-06-23 20:14:25 - gui.main_window - INFO - 📤 Final file path to upload: C:/Users/<USER>/Pictures/23-06-25/edited/processed_resized\wood-handle-shaving-brush-stand.webp
2025-06-23 20:14:25 - gui.main_window - INFO - Uploading to Shopify: wood-handle-shaving-brush-stand.webp (alt: 'Shaving brush with wooden handle on a brown metal ...')
2025-06-23 20:14:25 - shopify.client - INFO - Starting GraphQL file upload for wood-handle-shaving-brush-stand.webp (size: 52450 bytes)
2025-06-23 20:14:25 - shopify.client - INFO - Creating staged upload for wood-handle-shaving-brush-stand.webp
2025-06-23 20:14:25 - shopify.client - INFO - Staged upload created successfully for wood-handle-shaving-brush-stand.webp
2025-06-23 20:14:25 - shopify.client - INFO - Uploading wood-handle-shaving-brush-stand.webp to staged URL
2025-06-23 20:14:26 - shopify.client - INFO - File wood-handle-shaving-brush-stand.webp uploaded to staged URL successfully
2025-06-23 20:14:26 - shopify.client - INFO - Creating file from staged upload
2025-06-23 20:14:26 - shopify.client - INFO - FileCreate response status: 200
2025-06-23 20:14:26 - shopify.client - INFO - FileCreate response data: {'data': {'fileCreate': {'files': [{'id': 'gid://shopify/MediaImage/37311006703847', 'fileStatus': 'UPLOADED', 'alt': 'Shaving brush with wooden handle on a brown metal stand next to a green shaving soap bowl for traditional wet shaving', 'createdAt': '2025-06-23T10:14:26Z', 'image': None}], 'userErrors': []}}, 'extensions': {'cost': {'requestedQueryCost': 21, 'actualQueryCost': 21, 'throttleStatus': {'maximumAvailable': 2000.0, 'currentlyAvailable': 1979, 'restoreRate': 100.0}}}}
2025-06-23 20:14:26 - shopify.client - INFO - Extracting file info from: {'id': 'gid://shopify/MediaImage/37311006703847', 'fileStatus': 'UPLOADED', 'alt': 'Shaving brush with wooden handle on a brown metal stand next to a green shaving soap bowl for traditional wet shaving', 'createdAt': '2025-06-23T10:14:26Z', 'image': None}
2025-06-23 20:14:26 - shopify.client - INFO - Base file info extracted: {'id': 'gid://shopify/MediaImage/37311006703847', 'alt': 'Shaving brush with wooden handle on a brown metal stand next to a green shaving soap bowl for traditional wet shaving', 'fileStatus': 'UPLOADED', 'createdAt': '2025-06-23T10:14:26Z'}
2025-06-23 20:14:26 - shopify.client - INFO - Image data not yet available (file still processing). Status: UPLOADED
2025-06-23 20:14:26 - shopify.client - INFO - File created successfully in Shopify with ID: gid://shopify/MediaImage/37311006703847
2025-06-23 20:14:26 - shopify.client - INFO - Successfully uploaded wood-handle-shaving-brush-stand.webp to Shopify via GraphQL
2025-06-23 20:14:26 - gui.main_window - INFO - Successfully uploaded wood-handle-shaving-brush-stand.webp to Shopify (ID: gid://shopify/MediaImage/37311006703847)
2025-06-23 20:14:26 - gui.main_window - INFO - 📝 Note: Image data will be available after Shopify processing completes
2025-06-23 20:14:26 - gui.main_window - INFO - 
📁 Processing image 6/7: DSC08156_resized.webp
2025-06-23 20:14:26 - gui.main_window - INFO - 📄 Original file path: C:/Users/<USER>/Pictures/23-06-25/edited/processed_resized\DSC08156_resized.webp
2025-06-23 20:14:26 - gui.main_window - INFO - Step 1/3: Starting AI analysis for DSC08156_resized.webp
2025-06-23 20:14:29 - ai_providers.base_provider - WARNING - Truncated alt_text to 125 characters
2025-06-23 20:14:29 - gui.main_window - INFO - AI analysis complete for DSC08156_resized.webp: wood-handle-shaving-brush
2025-06-23 20:14:29 - gui.main_window - INFO - Step 2/3: File rename requested for DSC08156_resized.webp
2025-06-23 20:14:29 - gui.main_window - INFO - Renaming DSC08156_resized.webp → wood-handle-shaving-brush
2025-06-23 20:14:29 - utils.file_manager - INFO - Renamed C:/Users/<USER>/Pictures/23-06-25/edited/processed_resized\DSC08156_resized.webp to C:/Users/<USER>/Pictures/23-06-25/edited/processed_resized\wood-handle-shaving-brush.webp
2025-06-23 20:14:29 - gui.main_window - INFO - Successfully renamed: DSC08156_resized.webp → wood-handle-shaving-brush.webp
2025-06-23 20:14:29 - gui.main_window - INFO - Step 3/3: Starting Shopify upload for wood-handle-shaving-brush.webp
2025-06-23 20:14:29 - gui.main_window - INFO - 📤 Final file path to upload: C:/Users/<USER>/Pictures/23-06-25/edited/processed_resized\wood-handle-shaving-brush.webp
2025-06-23 20:14:29 - gui.main_window - INFO - Uploading to Shopify: wood-handle-shaving-brush.webp (alt: 'Shaving brush with wooden handle and soft bristles...')
2025-06-23 20:14:29 - shopify.client - INFO - Starting GraphQL file upload for wood-handle-shaving-brush.webp (size: 36174 bytes)
2025-06-23 20:14:29 - shopify.client - INFO - Creating staged upload for wood-handle-shaving-brush.webp
2025-06-23 20:14:29 - shopify.client - INFO - Staged upload created successfully for wood-handle-shaving-brush.webp
2025-06-23 20:14:29 - shopify.client - INFO - Uploading wood-handle-shaving-brush.webp to staged URL
2025-06-23 20:14:30 - shopify.client - INFO - File wood-handle-shaving-brush.webp uploaded to staged URL successfully
2025-06-23 20:14:30 - shopify.client - INFO - Creating file from staged upload
2025-06-23 20:14:30 - shopify.client - INFO - FileCreate response status: 200
2025-06-23 20:14:30 - shopify.client - INFO - FileCreate response data: {'data': {'fileCreate': {'files': [{'id': 'gid://shopify/MediaImage/37311006736615', 'fileStatus': 'UPLOADED', 'alt': 'Shaving brush with wooden handle and soft bristles resting in a black silicone soap dish with white and green dishes in backg', 'createdAt': '2025-06-23T10:14:30Z', 'image': None}], 'userErrors': []}}, 'extensions': {'cost': {'requestedQueryCost': 21, 'actualQueryCost': 21, 'throttleStatus': {'maximumAvailable': 2000.0, 'currentlyAvailable': 1979, 'restoreRate': 100.0}}}}
2025-06-23 20:14:30 - shopify.client - INFO - Extracting file info from: {'id': 'gid://shopify/MediaImage/37311006736615', 'fileStatus': 'UPLOADED', 'alt': 'Shaving brush with wooden handle and soft bristles resting in a black silicone soap dish with white and green dishes in backg', 'createdAt': '2025-06-23T10:14:30Z', 'image': None}
2025-06-23 20:14:30 - shopify.client - INFO - Base file info extracted: {'id': 'gid://shopify/MediaImage/37311006736615', 'alt': 'Shaving brush with wooden handle and soft bristles resting in a black silicone soap dish with white and green dishes in backg', 'fileStatus': 'UPLOADED', 'createdAt': '2025-06-23T10:14:30Z'}
2025-06-23 20:14:30 - shopify.client - INFO - Image data not yet available (file still processing). Status: UPLOADED
2025-06-23 20:14:30 - shopify.client - INFO - File created successfully in Shopify with ID: gid://shopify/MediaImage/37311006736615
2025-06-23 20:14:30 - shopify.client - INFO - Successfully uploaded wood-handle-shaving-brush.webp to Shopify via GraphQL
2025-06-23 20:14:30 - gui.main_window - INFO - Successfully uploaded wood-handle-shaving-brush.webp to Shopify (ID: gid://shopify/MediaImage/37311006736615)
2025-06-23 20:14:30 - gui.main_window - INFO - 📝 Note: Image data will be available after Shopify processing completes
2025-06-23 20:14:30 - gui.main_window - INFO - 
📁 Processing image 7/7: DSC08160_resized.webp
2025-06-23 20:14:30 - gui.main_window - INFO - 📄 Original file path: C:/Users/<USER>/Pictures/23-06-25/edited/processed_resized\DSC08160_resized.webp
2025-06-23 20:14:30 - gui.main_window - INFO - Step 1/3: Starting AI analysis for DSC08160_resized.webp
2025-06-23 20:14:32 - gui.main_window - INFO - AI analysis complete for DSC08160_resized.webp: wood-handle-shaving-brush-bowl
2025-06-23 20:14:32 - gui.main_window - INFO - Step 2/3: File rename requested for DSC08160_resized.webp
2025-06-23 20:14:32 - gui.main_window - INFO - Renaming DSC08160_resized.webp → wood-handle-shaving-brush-bowl
2025-06-23 20:14:32 - utils.file_manager - INFO - Renamed C:/Users/<USER>/Pictures/23-06-25/edited/processed_resized\DSC08160_resized.webp to C:/Users/<USER>/Pictures/23-06-25/edited/processed_resized\wood-handle-shaving-brush-bowl.webp
2025-06-23 20:14:32 - gui.main_window - INFO - Successfully renamed: DSC08160_resized.webp → wood-handle-shaving-brush-bowl.webp
2025-06-23 20:14:32 - gui.main_window - INFO - Step 3/3: Starting Shopify upload for wood-handle-shaving-brush-bowl.webp
2025-06-23 20:14:32 - gui.main_window - INFO - 📤 Final file path to upload: C:/Users/<USER>/Pictures/23-06-25/edited/processed_resized\wood-handle-shaving-brush-bowl.webp
2025-06-23 20:14:32 - gui.main_window - INFO - Uploading to Shopify: wood-handle-shaving-brush-bowl.webp (alt: 'Shaving brush with wooden handle and soft bristles...')
2025-06-23 20:14:32 - shopify.client - INFO - Starting GraphQL file upload for wood-handle-shaving-brush-bowl.webp (size: 30918 bytes)
2025-06-23 20:14:32 - shopify.client - INFO - Creating staged upload for wood-handle-shaving-brush-bowl.webp
2025-06-23 20:14:33 - shopify.client - INFO - Staged upload created successfully for wood-handle-shaving-brush-bowl.webp
2025-06-23 20:14:33 - shopify.client - INFO - Uploading wood-handle-shaving-brush-bowl.webp to staged URL
2025-06-23 20:14:33 - shopify.client - INFO - File wood-handle-shaving-brush-bowl.webp uploaded to staged URL successfully
2025-06-23 20:14:33 - shopify.client - INFO - Creating file from staged upload
2025-06-23 20:14:34 - shopify.client - INFO - FileCreate response status: 200
2025-06-23 20:14:34 - shopify.client - INFO - FileCreate response data: {'data': {'fileCreate': {'files': [{'id': 'gid://shopify/MediaImage/37311006769383', 'fileStatus': 'UPLOADED', 'alt': 'Shaving brush with wooden handle and soft bristles next to a black shaving soap bowl for traditional wet shaving', 'createdAt': '2025-06-23T10:14:33Z', 'image': None}], 'userErrors': []}}, 'extensions': {'cost': {'requestedQueryCost': 21, 'actualQueryCost': 21, 'throttleStatus': {'maximumAvailable': 2000.0, 'currentlyAvailable': 1979, 'restoreRate': 100.0}}}}
2025-06-23 20:14:34 - shopify.client - INFO - Extracting file info from: {'id': 'gid://shopify/MediaImage/37311006769383', 'fileStatus': 'UPLOADED', 'alt': 'Shaving brush with wooden handle and soft bristles next to a black shaving soap bowl for traditional wet shaving', 'createdAt': '2025-06-23T10:14:33Z', 'image': None}
2025-06-23 20:14:34 - shopify.client - INFO - Base file info extracted: {'id': 'gid://shopify/MediaImage/37311006769383', 'alt': 'Shaving brush with wooden handle and soft bristles next to a black shaving soap bowl for traditional wet shaving', 'fileStatus': 'UPLOADED', 'createdAt': '2025-06-23T10:14:33Z'}
2025-06-23 20:14:34 - shopify.client - INFO - Image data not yet available (file still processing). Status: UPLOADED
2025-06-23 20:14:34 - shopify.client - INFO - File created successfully in Shopify with ID: gid://shopify/MediaImage/37311006769383
2025-06-23 20:14:34 - shopify.client - INFO - Successfully uploaded wood-handle-shaving-brush-bowl.webp to Shopify via GraphQL
2025-06-23 20:14:34 - gui.main_window - INFO - Successfully uploaded wood-handle-shaving-brush-bowl.webp to Shopify (ID: gid://shopify/MediaImage/37311006769383)
2025-06-23 20:14:34 - gui.main_window - INFO - 📝 Note: Image data will be available after Shopify processing completes
2025-06-23 20:14:34 - gui.main_window - INFO - Processing complete: 0/7 images processed successfully
2025-06-23 20:14:37 - __main__ - INFO - Application closing
