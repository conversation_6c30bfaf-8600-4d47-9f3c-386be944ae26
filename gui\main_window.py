"""
Main Window - Content Strategist Dashboard

This is the main dashboard for the Content Strategist system.
It features four tabs:
1. Dashboard - Main control panel with "Run Next Job" button and LLM selection
2. Content Plan - Database view with TreeView and management buttons
3. Idea Generator - Interface for generating new content ideas with LLM selection
4. Log - Real-time application logging
"""

import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox, filedialog, simpledialog
import threading
import json
from datetime import datetime
from typing import Dict, Any, List, Optional

from gui.settings_window import SettingsWindow
from gui.gui_utils import decode_freshness_score, safe_sort_key_for_freshness
from gui.dashboard_tab import DashboardTab
from gui.content_plan_tab import ContentPlanTab
from gui.idea_generator_tab import IdeaGeneratorTab
from gui.log_tab import LogTab
from gui.database_switcher_dialog import DatabaseSwitcherDialog
from core import config_manager
from core.llm_interface import AVAILABLE_MODELS
from database import ContentDatabase
from idea_generator import IdeaGenerator
from planner import ContentPlanner
from worker import ContentWorker
from core import shopify_poster


class MainWindow:
    """
    The main dashboard for the Content Strategist application.
    """

    def __init__(self, master, initial_db_path: str):
        self.master = master
        master.title("Content Strategist Dashboard")
        master.geometry("1400x900")
        master.resizable(True, True)

        # Initialize backend components with the specified database path
        self.db = ContentDatabase(db_path=initial_db_path)
        self.idea_generator = IdeaGenerator(db_instance=self.db)
        self.planner = ContentPlanner(db_instance=self.db, debug_mode=True)  # Enable debugging for freshness scoring
        self.worker = ContentWorker(db_instance=self.db)

        # Initialize variables
        self.is_running = False
        self.current_thread = None
        self.stop_requested = False  # Flag to request stopping operations
        self.debug_mode = True  # Enable GUI debug output for freshness score display
        
        # Batch automation tracking
        self.is_batch_running = False
        self.batch_current_job = 0
        self.batch_total_jobs = 0

        # LLM selection variables
        self.writing_llm_provider_var = tk.StringVar()
        self.writing_llm_model_var = tk.StringVar()
        self.idea_gen_llm_provider_var = tk.StringVar()
        self.idea_gen_llm_model_var = tk.StringVar()

        # Stuga product list cache
        self.stuga_product_list_cache: Optional[str] = None
        self.last_product_fetch_time: Optional[datetime] = None

        # Create main frame
        main_frame = ttk.Frame(master, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # Create the tabbed interface
        self.create_tabbed_interface(main_frame)

        # Create status bar
        self.create_status_bar(main_frame)

        # Create menu bar
        self.create_menu_bar()

        # Bind the close event
        master.protocol("WM_DELETE_WINDOW", self.on_closing)

        # Initialize LLM dropdowns after all tabs are created
        self.populate_llm_dropdowns()

        # Initial data load
        self.refresh_content_plan()
        self.update_dashboard_status()
        self.fetch_and_cache_stuga_products()
        self.log_message("Content Strategist Dashboard initialized successfully!")
    
    def create_tabbed_interface(self, parent):
        """Create the main tabbed interface."""
        # Create notebook for tabs
        self.notebook = ttk.Notebook(parent)
        self.notebook.pack(fill=tk.BOTH, expand=True)

        # Create tab instances
        self.dashboard_tab = DashboardTab(self.notebook, self, self.db, self.planner, self.worker, self.log_message)
        self.notebook.add(self.dashboard_tab, text="Dashboard")

        self.content_plan_tab = ContentPlanTab(self.notebook, self, self.db, self.planner, self.log_message)
        self.notebook.add(self.content_plan_tab, text="Content Plan")

        self.idea_generator_tab = IdeaGeneratorTab(self.notebook, self, self.db, self.idea_generator, self.log_message)
        self.notebook.add(self.idea_generator_tab, text="Idea Generator")

        self.log_tab = LogTab(self.notebook)
        self.notebook.add(self.log_tab, text="Log")

    def create_status_bar(self, parent):
        """Create the status bar at the bottom of the window."""
        self.status_bar = ttk.Frame(parent)
        self.status_bar.pack(side=tk.BOTTOM, fill=tk.X, pady=(5, 0))

        self.status_label = ttk.Label(self.status_bar, text="Ready", relief=tk.SUNKEN, anchor=tk.W)
        self.status_label.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 5))

        # Optional: Add additional status elements like progress bar
        self.status_progress = ttk.Progressbar(self.status_bar, mode='indeterminate', length=100)
        # Don't pack the progress bar initially - it will be shown when needed

    def set_status_bar_text(self, text: str, show_progress: bool = False):
        """Update the status bar text and optionally show/hide progress indicator."""
        if hasattr(self, 'status_label'):
            self.status_label.config(text=text)

            if show_progress:
                self.status_progress.pack(side=tk.RIGHT, padx=(5, 0))
                self.status_progress.start()
            else:
                self.status_progress.stop()
                self.status_progress.pack_forget()

    def create_menu_bar(self):
        """Create the menu bar."""
        menubar = tk.Menu(self.master)
        self.master.config(menu=menubar)
        
        # File menu
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="File", menu=file_menu)
        file_menu.add_command(label="Switch/Load Database...", command=self.open_database_switcher_dialog)
        file_menu.add_command(label="Create New Database...", command=self.create_new_database_dialog)
        file_menu.add_separator()
        file_menu.add_command(label="Settings", command=self.open_settings)
        file_menu.add_separator()
        file_menu.add_command(label="Exit", command=self.on_closing)
        
        # Tools menu
        tools_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Tools", menu=tools_menu)
        tools_menu.add_command(label="Database Statistics", command=self.show_database_stats)
        tools_menu.add_command(label="Refresh All Scores", command=self.refresh_all_scores)
        tools_menu.add_separator()
        tools_menu.add_command(label="🔍 Scoring Debug Info", command=self.show_scoring_debug)
        tools_menu.add_command(label="🧠 Embedding Status", command=self.show_embedding_status)
        tools_menu.add_command(label="🔄 Force Refresh GUI", command=self.force_refresh_gui)
        tools_menu.add_separator()
        tools_menu.add_command(label="Open Database File", command=self.open_database_file)
        tools_menu.add_command(label="Export to CSV", command=self.export_to_csv)
        tools_menu.add_command(label="Database Cleanup", command=self.open_cleanup_dialog)
        tools_menu.add_command(label="💥 Dangerous Operations", command=self.open_dangerous_operations_dialog)
        tools_menu.add_separator()
        tools_menu.add_command(label="🔄 Refresh Stuga Product List", command=lambda: self.fetch_and_cache_stuga_products(force_refresh=True))
        tools_menu.add_command(label="🛍️ View Cached Stuga Product List", command=self.show_cached_product_list)
        
        # Help menu
        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Help", menu=help_menu)
        help_menu.add_command(label="About", command=self.show_about)
    
    def create_dashboard_tab(self):
        """Create the Dashboard tab with improved layout."""
        dashboard_frame = ttk.Frame(self.notebook, padding="15")
        self.notebook.add(dashboard_frame, text="Dashboard")

        # Title
        title_label = ttk.Label(dashboard_frame, text="Content Strategist Dashboard",
                               font=("Arial", 16, "bold"))
        title_label.pack(pady=(0, 15))

        # Top row: Planning and Execution side by side
        top_row = ttk.Frame(dashboard_frame)
        top_row.pack(fill=tk.BOTH, expand=True, pady=(0, 15))

        # Left side: Job Planning
        planning_frame = ttk.LabelFrame(top_row, text="📋 Job Planning", padding="12")
        planning_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 8))

        # Plan job controls
        plan_controls = ttk.Frame(planning_frame)
        plan_controls.pack(fill=tk.X, pady=(0, 10))

        self.plan_job_button = ttk.Button(plan_controls, text="🔎 Plan Next Job",
                                        command=self.plan_next_job)
        self.plan_job_button.pack(side=tk.LEFT, padx=(0, 8))

        self.stop_button = ttk.Button(plan_controls, text="🛑 Stop",
                                     command=self.stop_operations, state=tk.DISABLED)
        self.stop_button.pack(side=tk.LEFT, padx=(0, 8))

        # Progress bar for planning
        self.job_progress = ttk.Progressbar(plan_controls, mode='indeterminate', length=150)

        # Next job info (expandable)
        ttk.Label(planning_frame, text="Top Auto-Candidate (from NEW/ON_HOLD):", font="-weight bold").pack(anchor=tk.W, pady=(8, 2))
        self.next_job_info = tk.Text(planning_frame, height=6, wrap=tk.WORD, state=tk.DISABLED)
        self.next_job_info.pack(fill=tk.BOTH, expand=True)

        # Right side: Job Execution
        self.execution_frame = ttk.LabelFrame(top_row, text="🚀 Job Execution", padding="12")
        self.execution_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=(8, 0))

        # Create execution controls
        self.create_execution_controls()

        # Middle row: Manual Entry (horizontal layout)
        manual_frame = ttk.LabelFrame(dashboard_frame, text="➕ Quick Add Content", padding="12")
        manual_frame.pack(fill=tk.X, pady=(0, 15))
        
        self.create_manual_entry_section(manual_frame)

        # Bottom row: Statistics (expandable)
        stats_frame = ttk.LabelFrame(dashboard_frame, text="📊 Database Status", padding="10")
        stats_frame.pack(fill=tk.BOTH, expand=True)

        self.stats_text = tk.Text(stats_frame, height=8, wrap=tk.WORD, state=tk.DISABLED)
        self.stats_text.pack(fill=tk.BOTH, expand=True)

        # Initialize execution controls to disabled state
        self.reset_execution_controls()
        self.populate_manual_entry_dropdowns()
        self.update_dashboard_status()

    def create_manual_entry_section(self, parent):
        """Create the manual entry section with improved responsive layout."""
        # Main grid
        entry_grid = ttk.Frame(parent)
        entry_grid.pack(fill=tk.X, pady=(0, 8))

        # Configure grid weights for proper expansion
        entry_grid.columnconfigure(1, weight=2)  # Keyword entry gets more space
        entry_grid.columnconfigure(3, weight=1)  # Craft combo expandable
        entry_grid.columnconfigure(5, weight=1)  # Pillar combo expandable

        # Row 1: Keyword, Craft, and Pillar
        ttk.Label(entry_grid, text="Keyword:").grid(row=0, column=0, sticky="w", padx=(0, 8), pady=5)
        self.manual_keyword_var = tk.StringVar()
        ttk.Entry(entry_grid, textvariable=self.manual_keyword_var).grid(row=0, column=1, sticky="ew", padx=(0, 15), pady=5)

        ttk.Label(entry_grid, text="Craft:").grid(row=0, column=2, sticky="w", padx=(0, 8), pady=5)
        self.manual_craft_var = tk.StringVar()
        self.manual_craft_combo = ttk.Combobox(entry_grid, textvariable=self.manual_craft_var, state="readonly")
        self.manual_craft_combo.grid(row=0, column=3, sticky="ew", padx=(0, 15), pady=5)
        self.manual_craft_combo.bind('<<ComboboxSelected>>', self.on_manual_craft_changed)

        ttk.Label(entry_grid, text="Pillar:").grid(row=0, column=4, sticky="w", padx=(0, 8), pady=5)
        self.manual_pillar_var = tk.StringVar()
        self.manual_pillar_combo = ttk.Combobox(entry_grid, textvariable=self.manual_pillar_var, state="readonly")
        self.manual_pillar_combo.grid(row=0, column=5, sticky="ew", pady=5)

        # Row 2: Angle and Add button
        ttk.Label(entry_grid, text="Angle:").grid(row=1, column=0, sticky="w", padx=(0, 8), pady=5)
        self.manual_angle_var = tk.StringVar()
        angle_entry = ttk.Entry(entry_grid, textvariable=self.manual_angle_var)
        angle_entry.grid(row=1, column=1, columnspan=4, sticky="ew", padx=(0, 15), pady=5)

        ttk.Button(entry_grid, text="➕ Add", command=self.add_manual_content_idea).grid(row=1, column=5, sticky="ew", pady=5)

    def create_execution_controls(self):
        """Create the execution controls section with compact layout."""
        # Initialize variables for execution controls
        self.current_planned_job = None
        self.last_job_execution_result = None  # Store last job output for View/Save functionality
        self.run_data_collection_var = tk.BooleanVar(value=True)
        self.run_blog_writing_var = tk.BooleanVar(value=True)
        self.run_publish_var = tk.BooleanVar(value=True)
        
        # Multiple data source checkboxes (replace single dropdown)
        self.use_serpapi_var = tk.BooleanVar(value=True)
        self.use_alsoasked_var = tk.BooleanVar(value=False)
        self.use_llm_suggestion_var = tk.BooleanVar(value=False)
        
        # Publishing controls
        self.publish_platform_var = tk.StringVar(value="Shopify")
        self.publish_as_draft_var = tk.BooleanVar(value=True)
        
        # Instructions
        instruction_label = ttk.Label(self.execution_frame,
                                     text="Plan a job first to enable execution controls.",
                                     font=("Arial", 9), foreground="gray")
        instruction_label.pack(pady=(0, 8))
        
        # Job Primed for Execution label
        ttk.Label(self.execution_frame, text="Job Primed for Execution:", font="-weight bold").pack(anchor=tk.W, pady=(0, 2))
        
        # Planned job details (expandable)
        self.planned_job_text = tk.Text(self.execution_frame, height=4, wrap=tk.WORD, state=tk.DISABLED)
        self.planned_job_text.pack(fill=tk.BOTH, expand=True, pady=(0, 8))

        # Execution options in a responsive grid
        options_frame = ttk.Frame(self.execution_frame)
        options_frame.pack(fill=tk.X, pady=(0, 8))

        # Configure grid weights for proper expansion
        options_frame.columnconfigure(1, weight=1)  # Settings column expands

        # Column 1: Steps
        steps_frame = ttk.LabelFrame(options_frame, text="Steps", padding="8")
        steps_frame.grid(row=0, column=0, sticky="nsew", padx=(0, 8))

        ttk.Checkbutton(steps_frame, text="SERP", variable=self.run_data_collection_var).pack(anchor=tk.W)
        ttk.Checkbutton(steps_frame, text="Write", variable=self.run_blog_writing_var).pack(anchor=tk.W)
        ttk.Checkbutton(steps_frame, text="Publish", variable=self.run_publish_var).pack(anchor=tk.W)

        # Column 2: Source & Platform
        settings_frame = ttk.LabelFrame(options_frame, text="Settings", padding="8")
        settings_frame.grid(row=0, column=1, sticky="nsew")

        # Configure settings grid for proper expansion
        settings_frame.columnconfigure(1, weight=1)  # Dropdown column expands

        # Data sources section
        ttk.Label(settings_frame, text="Data Sources:").grid(row=0, column=0, sticky="w", padx=(0, 8), pady=3)
        sources_subframe = ttk.Frame(settings_frame)
        sources_subframe.grid(row=0, column=1, sticky="w", pady=3)
        
        ttk.Checkbutton(sources_subframe, text="SerpApi", variable=self.use_serpapi_var).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Checkbutton(sources_subframe, text="AlsoAsked", variable=self.use_alsoasked_var).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Checkbutton(sources_subframe, text="LLM", variable=self.use_llm_suggestion_var).pack(side=tk.LEFT)

        # Platform row
        ttk.Label(settings_frame, text="Platform:").grid(row=1, column=0, sticky="w", padx=(0, 8), pady=3)
        platform_combo = ttk.Combobox(settings_frame, textvariable=self.publish_platform_var, state="readonly")
        platform_combo['values'] = ["Shopify", "WordPress"]
        platform_combo.grid(row=1, column=1, sticky="ew", pady=3)
        
        # Draft checkbox row
        ttk.Checkbutton(settings_frame, text="Publish as Draft", variable=self.publish_as_draft_var).grid(row=2, column=0, columnspan=2, sticky="w", pady=3)

        # LLM selection row - using a sub-frame for provider and model
        ttk.Label(settings_frame, text="LLM:").grid(row=3, column=0, sticky="w", padx=(0, 8), pady=3)
        llm_frame = ttk.Frame(settings_frame)
        llm_frame.grid(row=3, column=1, sticky="ew", pady=3)
        llm_frame.columnconfigure(1, weight=1)  # Model combo expands more

        self.writing_provider_combo = ttk.Combobox(llm_frame, textvariable=self.writing_llm_provider_var, state="readonly", width=10)
        self.writing_provider_combo.grid(row=0, column=0, sticky="w", padx=(0, 5))
        self.writing_provider_combo.bind('<<ComboboxSelected>>', self.on_writing_provider_changed)

        self.writing_model_combo = ttk.Combobox(llm_frame, textvariable=self.writing_llm_model_var, state="readonly")
        self.writing_model_combo.grid(row=0, column=1, sticky="ew")

        # Execute button and View Last Job Output button
        execute_frame = ttk.Frame(self.execution_frame)
        execute_frame.pack(pady=8)

        self.execute_job_button = ttk.Button(execute_frame, text="🚀 Execute Job",
                                           command=self.execute_planned_job, state=tk.DISABLED)
        self.execute_job_button.pack(side=tk.LEFT, padx=(0, 8))

        self.view_last_job_button = ttk.Button(execute_frame, text="📋 View Last Job Output",
                                             command=self.view_last_job_output, state=tk.DISABLED)
        self.view_last_job_button.pack(side=tk.LEFT, padx=(0, 8))

        # Progress bar for execution (initially hidden)
        self.execution_progress = ttk.Progressbar(execute_frame, mode='indeterminate', length=150)




    
    def create_context_menu(self):
        """Create the right-click context menu for the content tree."""
        self.context_menu = tk.Menu(self.master, tearoff=0)
        self.context_menu.add_command(label="🎯 Prime This for Execution", command=self.plan_selected_job)
        self.context_menu.add_command(label="⭐ Set High Priority (999)", command=self.prioritize_selected)
        self.context_menu.add_separator()
        self.context_menu.add_command(label="📝 Set Custom Score...", command=self.set_custom_score)
        self.context_menu.add_command(label="🔄 Move to Top", command=self.move_to_top)
        self.context_menu.add_command(label="⬇️ Move to Bottom", command=self.move_to_bottom)
        self.context_menu.add_separator()
        self.context_menu.add_command(label="❌ Veto/Reject", command=self.veto_selected)
        self.context_menu.add_command(label="🗑️ Delete", command=self.delete_selected)
        self.context_menu.add_separator()
        self.context_menu.add_command(label="📋 Copy Keyword", command=self.copy_keyword)
        self.context_menu.add_command(label="📄 View Details", command=self.view_details)
    
    def show_context_menu(self, event):
        """Show the context menu at the cursor position."""
        try:
            # Select the item under the cursor
            item = self.content_tree.identify_row(event.y)
            if item:
                self.content_tree.selection_set(item)
                self.content_tree.focus(item)
                
                # Show the context menu
                self.context_menu.post(event.x_root, event.y_root)
        except Exception as e:
            self.log_message(f"Error showing context menu: {e}", "error")
    
    def plan_selected_job(self):
        """Prime the selected job for execution (works on any status)."""
        if hasattr(self, 'content_plan_tab'):
            selected = self.content_plan_tab.content_tree.selection()
        else:
            self.log_message("Error: Content Plan Tab not available for job selection.", "error")
            messagebox.showerror("Error", "Content Plan Tab is not initialized.")
            return

        if not selected:
            messagebox.showwarning("No Selection", "Please select a content idea to prime for execution.")
            return

        try:
            item = self.content_plan_tab.content_tree.item(selected[0])
            content_id = int(item['text'])
            
            # Get the content details
            content = self.db.get_content_by_id(content_id)
            if not content:
                messagebox.showerror("Error", "Content not found in database.")
                return
            
            # 1. Database Operation: Mark as planned (if not already) and store
            if content.get('status') != 'PLANNED':
                self.db.update_content_status(content_id, 'PLANNED')
                # Refresh content data to get updated status
                content = self.db.get_content_by_id(content_id)
            
            # 2. Set this as the current planned job for execution
            self.current_planned_job = content
            
            # 3. Log the Action
            self.log_message(f"🎯 Primed job for execution: '{content['keyword']}' (ID: {content_id})", "success")
            
            # 4. Update planned job text on Dashboard
            self.show_execution_controls()
            
            # 5. Enable the Execute Job button
            self.execute_job_button.config(state=tk.NORMAL)
            
            # 6. Update "Next Job Candidate" Logic (recalculates scores and identifies new auto-candidate)
            self.update_next_job_info()
            
            # 7. Refresh Content Plan TreeView (uses updated next_job_candidate_id for highlighting)
            self.refresh_content_plan()
            
            # 8. Update Dashboard Statistics
            self.update_dashboard_status()
            
            # 9. Switch to Dashboard tab to show execution controls
            self.notebook.select(0)  # Switch to Dashboard tab
            
        except Exception as e:
            self.log_message(f"Error priming selected job for execution: {e}", "error")
    
    def set_custom_score(self):
        """Set a custom freshness score for the selected content."""
        if not hasattr(self, 'content_plan_tab'):
            self.log_message("Content Plan Tab not initialized for set_custom_score.", "error")
            return

        content_id = self.content_plan_tab.get_selected_content_id() # Get ID from the tab
        if content_id is None:
            messagebox.showwarning("No Selection", "Please select a content idea in the Content Plan to set score.")
            return

        try:
            # Fetch the keyword from the database using the ID for the dialog
            content_item = self.db.get_content_by_id(content_id)
            if not content_item:
                messagebox.showerror("Error", "Selected content not found in database.")
                return
            keyword = content_item.get('keyword', 'Unknown')
            
            # Create a simple dialog for score input
            score_window = tk.Toplevel(self.master)
            score_window.title("Set Custom Score")
            score_window.geometry("300x150")
            score_window.resizable(False, False)
            score_window.transient(self.master)
            score_window.grab_set()
            
            # Center the window
            score_window.update_idletasks()
            x = (score_window.winfo_screenwidth() // 2) - (300 // 2)
            y = (score_window.winfo_screenheight() // 2) - (150 // 2)
            score_window.geometry(f"300x150+{x}+{y}")
            
            main_frame = ttk.Frame(score_window, padding="15")
            main_frame.pack(fill=tk.BOTH, expand=True)
            
            ttk.Label(main_frame, text=f"Set score for:", font=("Arial", 10, "bold")).pack()
            ttk.Label(main_frame, text=f"'{keyword}'", wraplength=250).pack(pady=(0, 10))
            
            score_frame = ttk.Frame(main_frame)
            score_frame.pack(pady=5)
            
            ttk.Label(score_frame, text="Score (0-999):").pack(side=tk.LEFT, padx=(0, 10))
            score_var = tk.StringVar(value="100")
            score_entry = ttk.Entry(score_frame, textvariable=score_var, width=10)
            score_entry.pack(side=tk.LEFT)
            score_entry.focus()
            score_entry.select_range(0, tk.END)
            
            button_frame = ttk.Frame(main_frame)
            button_frame.pack(pady=15)
            
            def apply_score():
                try:
                    score = float(score_var.get())
                    if 0 <= score <= 999:
                        self.db.update_freshness_score(content_id, score)
                        self.log_message(f"📊 Set custom score {score} for '{keyword}' (ID: {content_id})", "success")
                        
                        # Update next job candidate FIRST (recalculate scores)
                        self.update_next_job_info()
                        
                        # Then refresh UI with the updated candidate
                        self.content_plan_tab.refresh_content_plan() # Refresh the tab that owns the tree
                        self.update_dashboard_status()
                        score_window.destroy()
                    else:
                        messagebox.showerror("Invalid Score", "Score must be between 0 and 999.", parent=score_window)
                except ValueError:
                    messagebox.showerror("Invalid Input", "Please enter a valid number.", parent=score_window)
            
            ttk.Button(button_frame, text="Apply", command=apply_score).pack(side=tk.LEFT, padx=(0, 10))
            ttk.Button(button_frame, text="Cancel", command=score_window.destroy).pack(side=tk.LEFT)
            
            # Bind Enter key to apply
            score_window.bind('<Return>', lambda e: apply_score())
            
        except Exception as e:
            self.log_message(f"Error setting custom score from MainWindow: {e}", "error")
    
    def move_to_top(self):
        """Move selected content to top of queue."""
        content_id = self.content_plan_tab.get_selected_content_id()
        if content_id is None:
            messagebox.showwarning("No Selection", "Please select a content idea to move.")
            return

        try:
            # 1. Database Operation: Set score higher than any existing score
            all_content = self.db.get_all_content()
            max_score = 0
            for content in all_content:
                try:
                    score = decode_freshness_score(content.get('freshness_score', 0))
                    max_score = max(max_score, score)
                except (ValueError, TypeError):
                    pass
            
            new_score = max_score + 10
            self.db.update_freshness_score(content_id, new_score)

            # 2. Log the Action
            self.log_message(f"⬆️ Moved content ID {content_id} to top (Score: {new_score})", "success")
            
            # 3. Update "Next Job Candidate" Logic FIRST (recalculates scores and identifies new top candidate)
            self.update_next_job_info()
            
            # 4. Refresh Content Plan TreeView (uses updated next_job_candidate_id for highlighting)
            self.content_plan_tab.refresh_content_plan()
            
            # 5. Update Dashboard Statistics
            self.update_dashboard_status()
            
        except Exception as e:
            self.log_message(f"Error moving to top: {e}", "error")
    
    def move_to_bottom(self):
        """Move selected content to bottom of queue."""
        content_id = self.content_plan_tab.get_selected_content_id()
        if content_id is None:
            messagebox.showwarning("No Selection", "Please select a content idea to move.")
            return

        try:
            # 1. Database Operation: Set score to 1 (very low priority)
            self.db.update_freshness_score(content_id, 1.0)
            
            # 2. Log the Action
            self.log_message(f"⬇️ Moved content ID {content_id} to bottom (Score: 1.0)", "success")
            
            # 3. Update "Next Job Candidate" Logic FIRST (recalculates scores and identifies new top candidate)
            self.update_next_job_info()
            
            # 4. Refresh Content Plan TreeView (uses updated next_job_candidate_id for highlighting)
            self.content_plan_tab.refresh_content_plan()
            
            # 5. Update Dashboard Statistics
            self.update_dashboard_status()
            
        except Exception as e:
            self.log_message(f"Error moving to bottom: {e}", "error")
    
    def copy_keyword(self):
        """Copy the selected keyword to clipboard."""
        content_id = self.content_plan_tab.get_selected_content_id()
        if content_id is None:
            messagebox.showwarning("No Selection", "Please select a content idea to copy.")
            return

        try:
            content = self.db.get_content_by_id(content_id)
            if not content:
                messagebox.showerror("Error", "Content not found in database.")
                return
            
            keyword = content.get('keyword', 'Unknown')
            
            # Copy to clipboard
            self.master.clipboard_clear()
            self.master.clipboard_append(keyword)
            
            self.log_message(f"📋 Copied keyword '{keyword}' to clipboard", "info")
            
        except Exception as e:
            self.log_message(f"Error copying keyword: {e}", "error")
    
    def view_details(self):
        """View detailed information about the selected content."""
        content_id = self.content_plan_tab.get_selected_content_id()
        if content_id is None:
            messagebox.showwarning("No Selection", "Please select a content idea to view details.")
            return

        try:
            # This will automatically update the detail pane
            self.content_plan_tab.on_content_selection_changed(None)
            
        except Exception as e:
            self.log_message(f"Error viewing details: {e}", "error")

    def create_idea_generator_tab(self):
        """Create the Idea Generator tab."""
        idea_frame = ttk.Frame(self.notebook, padding="10")
        self.notebook.add(idea_frame, text="Idea Generator")

        # Controls frame
        controls_frame = ttk.LabelFrame(idea_frame, text="Generation Settings", padding="10")
        controls_frame.pack(fill=tk.X, pady=(0, 10))

        # Controls grid
        controls_grid = ttk.Frame(controls_frame)
        controls_grid.pack(fill=tk.X)

        # Number of ideas
        ttk.Label(controls_grid, text="Number of ideas:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        self.num_ideas_var = tk.IntVar(value=5)
        ttk.Spinbox(controls_grid, from_=1, to=20, textvariable=self.num_ideas_var, width=8).grid(row=0, column=1, sticky=tk.W, padx=5, pady=5)

        # Target craft (optional)
        ttk.Label(controls_grid, text="Target craft (optional):").grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        self.target_craft_var = tk.StringVar()
        self.craft_combo = ttk.Combobox(controls_grid, textvariable=self.target_craft_var, state="readonly", width=25)
        self.craft_combo.grid(row=1, column=1, sticky=tk.W, padx=5, pady=5)

        # Target creativity vector (optional)
        ttk.Label(controls_grid, text="Creativity approach (optional):").grid(row=2, column=0, sticky=tk.W, padx=5, pady=5)
        self.target_vector_var = tk.StringVar()
        self.vector_combo = ttk.Combobox(controls_grid, textvariable=self.target_vector_var, state="readonly", width=25)
        self.vector_combo.grid(row=2, column=1, sticky=tk.W, padx=5, pady=5)

        # LLM Selection for Idea Generation
        ttk.Label(controls_grid, text="Idea Gen LLM Provider:").grid(row=0, column=2, sticky=tk.W, padx=(20, 5), pady=5)
        self.idea_gen_provider_combo = ttk.Combobox(controls_grid, textvariable=self.idea_gen_llm_provider_var,
                                                   state="readonly", width=15)
        self.idea_gen_provider_combo.grid(row=0, column=3, padx=5, pady=5)
        self.idea_gen_provider_combo.bind('<<ComboboxSelected>>', self.on_idea_gen_provider_changed)

        ttk.Label(controls_grid, text="Idea Gen LLM Model:").grid(row=1, column=2, sticky=tk.W, padx=(20, 5), pady=5)
        self.idea_gen_model_combo = ttk.Combobox(controls_grid, textvariable=self.idea_gen_llm_model_var,
                                                state="readonly", width=25)
        self.idea_gen_model_combo.grid(row=1, column=3, padx=5, pady=5)

        # Generate button and progress bar frame
        button_frame = ttk.Frame(controls_frame)
        button_frame.pack(pady=15)

        self.generate_ideas_button = ttk.Button(button_frame, text="💡 Generate New Ideas", command=self.generate_ideas)
        self.generate_ideas_button.pack(side=tk.LEFT, padx=(0, 10))

        # Progress bar for idea generation (initially hidden)
        self.idea_progress = ttk.Progressbar(button_frame, mode='indeterminate', length=200)

        # Results area
        results_frame = ttk.LabelFrame(idea_frame, text="Generated Ideas", padding="8")
        results_frame.pack(fill=tk.BOTH, expand=True)

        self.ideas_text = scrolledtext.ScrolledText(results_frame, wrap=tk.WORD, height=12)
        self.ideas_text.pack(fill=tk.BOTH, expand=True)

        # Load creativity vectors and crafts
        self.load_generation_options()
    
    def create_log_tab(self):
        """Create the Log tab."""
        log_frame = ttk.Frame(self.notebook, padding="8")
        self.notebook.add(log_frame, text="Log")
        
        # Log controls
        log_controls = ttk.Frame(log_frame)
        log_controls.pack(fill=tk.X, pady=(0, 8))
        
        ttk.Button(log_controls, text="Clear Log", command=self.clear_log).pack(side=tk.LEFT)
        
        # Log text area (more compact)
        self.log_text = scrolledtext.ScrolledText(log_frame, wrap=tk.WORD, height=20)
        self.log_text.pack(fill=tk.BOTH, expand=True)
        
        # Configure log text tags for different message types
        self.log_text.tag_configure("info", foreground="black")
        self.log_text.tag_configure("success", foreground="green")
        self.log_text.tag_configure("warning", foreground="orange")
        self.log_text.tag_configure("error", foreground="red")
    
    def log_message(self, message: str, level: str = "info"):
        """Add a message to the log with timestamp."""
        # Delegate to log tab if it exists
        if hasattr(self, 'log_tab'):
            self.log_tab.log_message(message, level)
        else:
            # Fallback for early initialization
            timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            formatted_message = f"[{timestamp}] {message}"
            print(formatted_message)

    def populate_llm_dropdowns(self):
        """Populate LLM provider and model dropdowns."""
        try:
            # Delegate to tab classes
            if hasattr(self, 'dashboard_tab'):
                self.dashboard_tab.populate_llm_dropdowns()
            if hasattr(self, 'idea_generator_tab'):
                self.idea_generator_tab.populate_llm_dropdowns()

        except Exception as e:
            self.log_message(f"Error populating LLM dropdowns: {e}", "error")

    def on_writing_provider_changed(self, event=None):
        """Handle writing LLM provider selection change."""
        try:
            provider = self.writing_llm_provider_var.get()
            if provider and provider in AVAILABLE_MODELS:
                models = AVAILABLE_MODELS[provider]
                self.writing_model_combo['values'] = models

                # Set default model
                default_model = config_manager.get_pipeline_setting('writing_llm_model')
                if default_model and default_model in models:
                    self.writing_llm_model_var.set(default_model)
                elif models:
                    self.writing_llm_model_var.set(models[0])

        except Exception as e:
            self.log_message(f"Error updating writing models: {e}", "error")

    def on_idea_gen_provider_changed(self, event=None):
        """Handle idea generation LLM provider selection change."""
        try:
            provider = self.idea_gen_llm_provider_var.get()
            if provider and provider in AVAILABLE_MODELS:
                models = AVAILABLE_MODELS[provider]
                self.idea_gen_model_combo['values'] = models

                # Set default model
                default_model = config_manager.get_pipeline_setting('analysis_llm_model')
                if default_model and default_model in models:
                    self.idea_gen_llm_model_var.set(default_model)
                elif models:
                    self.idea_gen_llm_model_var.set(models[0])

        except Exception as e:
            self.log_message(f"Error updating idea generation models: {e}", "error")

    def stop_operations(self):
        """Stop any running operations."""
        if self.is_running:
            self.stop_requested = True
            self.log_message("🛑 Stop requested - operations will terminate soon...", "warning")
            self.stop_button.config(state=tk.DISABLED)
        else:
            messagebox.showinfo("No Operations", "No operations are currently running.")

    def update_dashboard_status(self):
        """Update the dashboard status displays."""
        try:
            # Delegate to dashboard tab
            if hasattr(self, 'dashboard_tab'):
                self.dashboard_tab.update_dashboard_status()

        except Exception as e:
            self.log_message(f"Error updating dashboard status: {e}", "error")

    def update_next_job_info(self):
        """Update the next job information display."""
        try:
            # Delegate to dashboard tab
            if hasattr(self, 'dashboard_tab'):
                self.dashboard_tab.update_next_job_info()

        except Exception as e:
            self.log_message(f"Error updating next job info: {e}", "error")

    def plan_next_job(self):
        """Plan the next content job (select and mark as PLANNED)."""
        if self.is_running:
            messagebox.showwarning("Operation Running", "An operation is already running. Please wait for it to complete.")
            return

        def plan_thread():
            try:
                self.is_running = True
                self.stop_requested = False
                self.master.after(0, lambda: self.plan_job_button.config(state=tk.DISABLED, text="Planning..."))
                self.master.after(0, lambda: self.stop_button.config(state=tk.NORMAL))
                self.master.after(0, lambda: self.job_progress.pack(side=tk.LEFT, padx=(0, 10)))
                self.master.after(0, lambda: self.job_progress.start(10))

                self.log_message("🔎 Planning next job...", "info")

                # Get business logic settings
                freshness_threshold = float(config_manager.get_pipeline_setting('freshness_threshold') or 70.0)
                pillar_weights_str = config_manager.get_pipeline_setting('pillar_weights') or '{}'
                try:
                    pillar_weights = json.loads(pillar_weights_str)
                except json.JSONDecodeError:
                    pillar_weights = {}  # Fallback to empty dict

                # Select next job
                job = self.planner.select_next_job(pillar_weights, freshness_threshold)

                if not job:
                    self.log_message("❌ No job selected - threshold not met or no content available", "warning")
                    return

                # Store the planned job
                self.current_planned_job = job
                self.log_message(f"✅ Planned job: '{job['keyword']}' (Score: {job['freshness_score']:.1f})", "success")

                # Update UI to show execution controls
                self.master.after(0, self.show_execution_controls)

                # Refresh displays
                self.master.after(0, self.refresh_content_plan)
                self.master.after(0, self.update_dashboard_status)

            except Exception as e:
                self.log_message(f"❌ Error planning job: {e}", "error")
            finally:
                self.is_running = False
                self.stop_requested = False
                self.master.after(0, lambda: self.plan_job_button.config(state=tk.NORMAL, text="🔎 Plan Next Job"))
                self.master.after(0, lambda: self.stop_button.config(state=tk.DISABLED))
                self.master.after(0, lambda: self.job_progress.stop())
                self.master.after(0, lambda: self.job_progress.pack_forget())

        self.current_thread = threading.Thread(target=plan_thread, daemon=True)
        self.current_thread.start()

    def show_execution_controls(self):
        """Show the execution controls section with planned job details."""
        if not self.current_planned_job:
            return

        # Update planned job details (compact format)
        job = self.current_planned_job
        job_text = f"Planned: {job['keyword']} | {job['pillar']} | Score: {job['freshness_score']:.1f}"

        self.planned_job_text.config(state=tk.NORMAL)
        self.planned_job_text.delete('1.0', tk.END)
        self.planned_job_text.insert('1.0', job_text)
        self.planned_job_text.config(state=tk.DISABLED)

        # Enable execution controls
        self.execute_job_button.config(state=tk.NORMAL)

    def reset_execution_controls(self):
        """Reset the execution controls to their initial state."""
        # Clear planned job details
        self.planned_job_text.config(state=tk.NORMAL)
        self.planned_job_text.delete('1.0', tk.END)
        self.planned_job_text.insert('1.0', "No job planned. Use 'Plan Next Job' to select a job.")
        self.planned_job_text.config(state=tk.DISABLED)

        # Disable execute button
        self.execute_job_button.config(state=tk.DISABLED)

        # Keep View Last Job Output button enabled if we have results
        if not self.last_job_execution_result:
            self.view_last_job_button.config(state=tk.DISABLED)

    def execute_planned_job(self, include_product_links: bool = True):
        """Execute the planned job with selected options."""
        if not self.dashboard_tab.current_planned_job:
            messagebox.showwarning("No Planned Job", "Please plan a job first using the 'Plan Next Job' button.")
            return

        if self.is_running:
            messagebox.showwarning("Operation Running", "An operation is already running. Please wait for it to complete.")
            return

        def execute_thread():
            try:
                self.is_running = True
                self.stop_requested = False
                self.master.after(0, lambda: self.dashboard_tab.execute_job_button.config(state=tk.DISABLED, text="Executing..."))
                self.master.after(0, lambda: self.dashboard_tab.stop_button.config(state=tk.NORMAL))
                self.master.after(0, lambda: self.dashboard_tab.execution_progress.pack(side=tk.LEFT, padx=(0, 10)))
                self.master.after(0, lambda: self.dashboard_tab.execution_progress.start(10))

                job = self.dashboard_tab.current_planned_job.copy()

                # Get execution options from dashboard tab
                run_data_collection = self.dashboard_tab.run_data_collection_var.get()
                run_blog_writing = self.dashboard_tab.run_blog_writing_var.get()
                run_publish = self.dashboard_tab.run_publish_var.get()
                # Get multiple data source settings
                use_serpapi = self.dashboard_tab.use_serpapi_var.get()
                use_alsoasked = self.dashboard_tab.use_alsoasked_var.get()
                use_llm_suggestion = self.dashboard_tab.use_llm_suggestion_var.get()
                # Get publishing settings
                platform = self.dashboard_tab.publish_platform_var.get()
                publish_as_draft = self.dashboard_tab.publish_as_draft_var.get()

                self.log_message(f"🚀 Executing planned job: '{job['keyword']}'", "info")
                self.log_message(f"Steps: Data Collection={run_data_collection}, Writing={run_blog_writing}, Publishing={run_publish}", "info")
                self.log_message(f"Data Sources: SerpApi={use_serpapi}, AlsoAsked={use_alsoasked}, LLM={use_llm_suggestion}", "info")
                self.log_message(f"Platform: {platform}, Draft: {publish_as_draft}", "info")

                # Get selected LLM settings from dashboard tab
                writing_provider = self.dashboard_tab.writing_llm_provider_var.get()
                writing_model = self.dashboard_tab.writing_llm_model_var.get()

                if writing_provider and writing_model:
                    self.log_message(f"Using {writing_provider} ({writing_model}) for content generation", "info")
                    job['override_writing_llm'] = writing_provider
                    job['override_writing_model'] = writing_model

                # Prepare product list string based on toggle
                if include_product_links:
                    # Make sure the product cache is populated
                    self.fetch_and_cache_stuga_products()
                    product_list_from_cache = self.stuga_product_list_cache
                    if product_list_from_cache and "Error" not in product_list_from_cache and "unavailable" not in product_list_from_cache:
                        stuga_product_list_str_for_prompt = product_list_from_cache
                    else:
                        stuga_product_list_str_for_prompt = "Product list data is currently unavailable. No product links can be generated."
                        self.log_message("Warning: Product linking enabled, but product list cache is empty or contains an error.", "warning")
                else:
                    stuga_product_list_str_for_prompt = "Product linking is disabled for this article. Do not attempt to include product links."

                # Execute the job with options
                result = self.worker.execute_job(
                    job,
                    stuga_product_list_str=stuga_product_list_str_for_prompt,
                    run_data_collection=run_data_collection,
                    run_blog_writing=run_blog_writing,
                    run_publish=run_publish,
                    use_serpapi=use_serpapi,
                    use_alsoasked=use_alsoasked,
                    use_llm_suggestion=use_llm_suggestion,
                    publish_platform=platform,
                    publish_as_draft=publish_as_draft
                )

                # Store the result for potential viewing
                self.last_job_execution_result = result

                # Enable the View Last Job Output button if we have any results to show
                # Check if any step produced results
                has_results = (
                    result.get('serp_data') or
                    result.get('analysis_results', {}).get('raw_analysis') or
                    result.get('blog_content') or
                    result.get('meta_description')
                )

                if has_results:
                    self.master.after(0, lambda: self.dashboard_tab.view_last_job_button.config(state=tk.NORMAL))
                    self.log_message("📋 Job output is available for viewing", "info")

                if result['success']:
                    if result.get('platform_url'):
                        self.log_message(f"🎉 Job completed successfully! Published at: {result['platform_url']}", "success")
                    else:
                        self.log_message(f"🎉 Job completed successfully! (Publishing was skipped)", "success")

                        # If content was written but not published, show helpful message
                        if result.get('blog_content') and not result.get('platform_url'):
                            self.log_message("💡 Content was generated and saved in database. You can access it later.", "info")
                else:
                    self.log_message(f"❌ Job failed: {'; '.join(result['errors'])}", "error")

                # Clear planned job and reset execution controls
                self.dashboard_tab.current_planned_job = None
                self.master.after(0, self.dashboard_tab.reset_execution_controls)

                # Refresh displays
                self.master.after(0, self.refresh_content_plan)
                self.master.after(0, self.update_dashboard_status)

            except Exception as e:
                self.log_message(f"❌ Error executing job: {e}", "error")
            finally:
                self.is_running = False
                self.stop_requested = False
                self.master.after(0, lambda: self.dashboard_tab.execute_job_button.config(state=tk.NORMAL, text="🚀 Execute Job"))
                self.master.after(0, lambda: self.dashboard_tab.stop_button.config(state=tk.DISABLED))
                self.master.after(0, lambda: self.dashboard_tab.execution_progress.stop())
                self.master.after(0, lambda: self.dashboard_tab.execution_progress.pack_forget())

        self.current_thread = threading.Thread(target=execute_thread, daemon=True)
        self.current_thread.start()

    def fetch_and_cache_stuga_products(self, force_refresh: bool = False):
        """
        Fetches and caches the Stuga product list from Shopify.

        Args:
            force_refresh: If True, forces a refresh even if cache is valid

        Returns:
            The cached product list string (may be from previous fetch if threading)
        """
        # Check if we should use cached data
        if not force_refresh and self.stuga_product_list_cache is not None:
            self.log_message("Using cached Stuga product list.", "info")
            return self.stuga_product_list_cache

        # Start the fetch process
        self.log_message("Fetching Stuga product list via Shopify API...", "info")
        self.set_status_bar_text("Fetching Shopify products...", show_progress=True)

        def _fetch_products_thread_target():
            """Thread target for fetching products."""
            product_list_str = "Stuga product list is currently unavailable."
            try:
                shop_url = config_manager.get_setting('SHOPIFY', 'shop_url')
                api_token = config_manager.get_setting('SHOPIFY', 'api_token')
                if shop_url and api_token:
                    product_list_str = shopify_poster.get_formatted_product_list(
                        shop_url, api_token
                    )
                    self.stuga_product_list_cache = product_list_str
                    self.last_product_fetch_time = datetime.now()
                    self.log_message(f"Successfully fetched and cached {len(product_list_str.splitlines())} Stuga products.", "success")
                else:
                    self.log_message("Shopify API not configured. Cannot fetch product list.", "warning")
                    self.stuga_product_list_cache = product_list_str  # "unavailable"
            except Exception as e:
                self.log_message(f"Error fetching Stuga product list: {e}", "error")
                self.stuga_product_list_cache = f"Error fetching products: {e}"
            finally:
                # Update status bar back to ready
                self.master.after(0, lambda: self.set_status_bar_text("Ready"))

        threading.Thread(target=_fetch_products_thread_target, daemon=True).start()
        return self.stuga_product_list_cache

    def show_cached_product_list(self):
        """Show the cached product list in a popup window."""
        # Check if we have cached data
        if not self.stuga_product_list_cache:
            messagebox.showinfo("No Product Data",
                              "No product list is currently cached.\n\n"
                              "Use 'Refresh Stuga Product List' from the Tools menu to fetch product data.")
            return

        # Check if it's an error message
        if "Error" in self.stuga_product_list_cache or "unavailable" in self.stuga_product_list_cache:
            messagebox.showinfo("Product Data Error",
                              f"Product list cache contains an error:\n\n{self.stuga_product_list_cache}")
            return

        # Create the popup window
        product_window = tk.Toplevel(self.master)
        product_window.title("Cached Stuga Product List")
        product_window.geometry("800x600")
        product_window.resizable(True, True)

        # Create main frame
        main_frame = ttk.Frame(product_window, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # Title and info
        title_label = ttk.Label(main_frame, text="Cached Stuga Product List",
                               font=("Arial", 14, "bold"))
        title_label.pack(pady=(0, 5))

        # Show cache timestamp if available
        if self.last_product_fetch_time:
            time_str = self.last_product_fetch_time.strftime("%Y-%m-%d %H:%M:%S")
            info_label = ttk.Label(main_frame, text=f"Last fetched: {time_str}",
                                  font=("Arial", 9), foreground="gray")
            info_label.pack(pady=(0, 10))

        # Product list display
        product_text = scrolledtext.ScrolledText(main_frame, wrap=tk.WORD, state=tk.NORMAL)
        product_text.pack(fill=tk.BOTH, expand=True, pady=(0, 10))

        # Insert the cached product list
        product_text.insert('1.0', self.stuga_product_list_cache)
        product_text.config(state=tk.DISABLED)  # Make it read-only

        # Button frame
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=(5, 0))

        def refresh_and_update():
            """Refresh the product list and update the display."""
            self.fetch_and_cache_stuga_products(force_refresh=True)
            # Wait a moment for the fetch to complete, then update display
            def update_display():
                if self.stuga_product_list_cache:
                    product_text.config(state=tk.NORMAL)
                    product_text.delete('1.0', tk.END)
                    product_text.insert('1.0', self.stuga_product_list_cache)
                    product_text.config(state=tk.DISABLED)

                    # Update timestamp
                    if self.last_product_fetch_time:
                        time_str = self.last_product_fetch_time.strftime("%Y-%m-%d %H:%M:%S")
                        info_label.config(text=f"Last fetched: {time_str}")

            # Schedule the update after a short delay to allow fetch to complete
            product_window.after(2000, update_display)

        ttk.Button(button_frame, text="🔄 Refresh List",
                  command=refresh_and_update).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="Close",
                  command=product_window.destroy).pack(side=tk.RIGHT)

    def view_last_job_output(self):
        """Open a window to view and save the last job execution results."""
        if not self.last_job_execution_result:
            messagebox.showwarning("No Job Output", "No job execution results available to view.")
            return

        try:
            # Create toplevel window
            output_window = tk.Toplevel(self.master)
            output_window.title("Last Job Output")
            output_window.geometry("1000x700")
            output_window.resizable(True, True)
            output_window.transient(self.master)

            # Center the window
            output_window.update_idletasks()
            x = (output_window.winfo_screenwidth() // 2) - (1000 // 2)
            y = (output_window.winfo_screenheight() // 2) - (700 // 2)
            output_window.geometry(f"1000x700+{x}+{y}")

            # Main frame
            main_frame = ttk.Frame(output_window, padding="10")
            main_frame.pack(fill=tk.BOTH, expand=True)

            # Title
            title_label = ttk.Label(main_frame, text="Last Job Execution Results",
                                   font=("Arial", 14, "bold"))
            title_label.pack(pady=(0, 10))

            # Create notebook for tabs
            notebook = ttk.Notebook(main_frame)
            notebook.pack(fill=tk.BOTH, expand=True, pady=(0, 10))

            result = self.last_job_execution_result
            tabs_created = 0

            # Data Collection Tab
            if result.get('serp_data'):
                data_frame = ttk.Frame(notebook)
                notebook.add(data_frame, text="Data Collection")
                tabs_created += 1

                for source, data in result['serp_data'].items():
                    source_frame = ttk.LabelFrame(data_frame, text=f"{source.title()} Output", padding="8")
                    source_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 5))

                    source_text = scrolledtext.ScrolledText(source_frame, wrap=tk.WORD, height=8)
                    source_text.pack(fill=tk.BOTH, expand=True)
                    source_text.insert('1.0', json.dumps(data, indent=2))
                    source_text.config(state=tk.DISABLED)

            # Analysis Tab
            if result.get('analysis_results', {}).get('raw_analysis'):
                analysis_frame = ttk.Frame(notebook)
                notebook.add(analysis_frame, text="LLM Analysis")
                tabs_created += 1

                analysis_label_frame = ttk.LabelFrame(analysis_frame, text="Combined LLM Analysis", padding="8")
                analysis_label_frame.pack(fill=tk.BOTH, expand=True)

                analysis_text = scrolledtext.ScrolledText(analysis_label_frame, wrap=tk.WORD)
                analysis_text.pack(fill=tk.BOTH, expand=True)
                analysis_text.insert('1.0', result['analysis_results']['raw_analysis'])
                analysis_text.config(state=tk.DISABLED)

            # Blog Content Tab
            if result.get('blog_content'):
                blog_frame = ttk.Frame(notebook)
                notebook.add(blog_frame, text="Generated Content")
                tabs_created += 1

                # Blog content section
                blog_label_frame = ttk.LabelFrame(blog_frame, text="Generated Blog Content", padding="8")
                blog_label_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 5))

                blog_text = scrolledtext.ScrolledText(blog_label_frame, wrap=tk.WORD)
                blog_text.pack(fill=tk.BOTH, expand=True)
                blog_text.insert('1.0', result['blog_content'])
                blog_text.config(state=tk.DISABLED)

                # Meta description section
                if result.get('meta_description'):
                    meta_frame = ttk.LabelFrame(blog_frame, text="Meta Description", padding="8")
                    meta_frame.pack(fill=tk.X, pady=(5, 0))

                    meta_text = tk.Text(meta_frame, wrap=tk.WORD, height=3)
                    meta_text.pack(fill=tk.X)
                    meta_text.insert('1.0', result['meta_description'])
                    meta_text.config(state=tk.DISABLED)

            # If no tabs were created, show a message
            if tabs_created == 0:
                no_data_frame = ttk.Frame(notebook)
                notebook.add(no_data_frame, text="No Data")

                no_data_label = ttk.Label(no_data_frame,
                                         text="No job output data available to display.\nThis shouldn't happen if the button was enabled correctly.",
                                         font=("Arial", 12), justify=tk.CENTER)
                no_data_label.pack(expand=True, pady=50)

            # Action buttons frame
            button_frame = ttk.Frame(main_frame)
            button_frame.pack(fill=tk.X, pady=(10, 0))

            # Create action buttons
            self.create_output_action_buttons(button_frame, result, output_window)

        except Exception as e:
            self.log_message(f"Error viewing last job output: {e}", "error")
            messagebox.showerror("Error", f"Failed to display job output: {e}")

    def create_output_action_buttons(self, parent_frame, result, window):
        """Create action buttons for the job output window."""
        try:
            # Save All Output as JSON
            ttk.Button(parent_frame, text="💾 Save All Output as JSON",
                      command=lambda: self.save_all_output_json(result)).pack(side=tk.LEFT, padx=(0, 5))

            # Copy Analysis to Clipboard
            if result.get('analysis_results', {}).get('raw_analysis'):
                ttk.Button(parent_frame, text="📋 Copy Analysis",
                          command=lambda: self.copy_to_clipboard(result['analysis_results']['raw_analysis'])).pack(side=tk.LEFT, padx=5)

            # Copy Blog HTML to Clipboard
            if result.get('blog_content'):
                ttk.Button(parent_frame, text="📋 Copy Blog HTML",
                          command=lambda: self.copy_to_clipboard(result['blog_content'])).pack(side=tk.LEFT, padx=5)

            # Save Blog as HTML
            if result.get('blog_content'):
                ttk.Button(parent_frame, text="💾 Save Blog as HTML",
                          command=lambda: self.save_blog_html(result['blog_content'])).pack(side=tk.LEFT, padx=5)

            # Close button
            ttk.Button(parent_frame, text="❌ Close",
                      command=window.destroy).pack(side=tk.RIGHT, padx=(5, 0))

        except Exception as e:
            self.log_message(f"Error creating action buttons: {e}", "error")

    def save_all_output_json(self, result):
        """Save the entire job result as a JSON file."""
        try:
            filename = filedialog.asksaveasfilename(
                title="Save Job Output as JSON",
                defaultextension=".json",
                filetypes=[("JSON files", "*.json"), ("All files", "*.*")],
                initialname=f"job_output_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            )

            if filename:
                with open(filename, 'w', encoding='utf-8') as f:
                    json.dump(result, f, indent=2, ensure_ascii=False)

                self.log_message(f"💾 Saved job output to: {filename}", "success")
                messagebox.showinfo("Success", f"Job output saved to:\n{filename}")

        except Exception as e:
            self.log_message(f"Error saving job output: {e}", "error")
            messagebox.showerror("Error", f"Failed to save job output: {e}")

    def copy_to_clipboard(self, text):
        """Copy text to clipboard."""
        try:
            self.master.clipboard_clear()
            self.master.clipboard_append(text)
            self.log_message("📋 Content copied to clipboard", "info")
            messagebox.showinfo("Success", "Content copied to clipboard!")

        except Exception as e:
            self.log_message(f"Error copying to clipboard: {e}", "error")
            messagebox.showerror("Error", f"Failed to copy to clipboard: {e}")

    def save_blog_html(self, html_content):
        """Save blog content as an HTML file."""
        try:
            filename = filedialog.asksaveasfilename(
                title="Save Blog Content as HTML",
                defaultextension=".html",
                filetypes=[("HTML files", "*.html"), ("All files", "*.*")],
                initialname=f"blog_content_{datetime.now().strftime('%Y%m%d_%H%M%S')}.html"
            )

            if filename:
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(html_content)

                self.log_message(f"💾 Saved blog content to: {filename}", "success")
                messagebox.showinfo("Success", f"Blog content saved to:\n{filename}")

        except Exception as e:
            self.log_message(f"Error saving blog content: {e}", "error")
            messagebox.showerror("Error", f"Failed to save blog content: {e}")

    def populate_manual_entry_dropdowns(self):
        """Populate the craft and pillar dropdowns for manual entry."""
        try:
            # Get business pillars from config
            business_pillars = config_manager.get_pipeline_setting('business_pillars') or ""

            crafts = []
            self.craft_pillar_mapping = {}

            for line in business_pillars.split('\n'):
                line = line.strip()
                if '=' in line:
                    craft, pillars_str = line.split('=', 1)
                    craft = craft.strip()
                    pillars = [p.strip() for p in pillars_str.split(',') if p.strip()]

                    if craft and pillars:
                        crafts.append(craft)
                        self.craft_pillar_mapping[craft] = pillars

            # Update craft dropdown
            self.manual_craft_combo['values'] = crafts

            # Clear pillar dropdown
            self.manual_pillar_combo['values'] = []

        except Exception as e:
            self.log_message(f"Error populating manual entry dropdowns: {e}", "error")

    def on_manual_craft_changed(self, event=None):
        """Handle craft selection change in manual entry."""
        try:
            selected_craft = self.manual_craft_var.get()
            if selected_craft in self.craft_pillar_mapping:
                pillars = self.craft_pillar_mapping[selected_craft]
                self.manual_pillar_combo['values'] = pillars
                # Clear current pillar selection
                self.manual_pillar_var.set("")
            else:
                self.manual_pillar_combo['values'] = []
                self.manual_pillar_var.set("")
        except Exception as e:
            self.log_message(f"Error updating pillar dropdown: {e}", "error")

    def add_manual_content_idea(self):
        """Add a manually entered content idea to the database."""
        try:
            # Get input values
            keyword = self.manual_keyword_var.get().strip()
            craft = self.manual_craft_var.get().strip()
            pillar = self.manual_pillar_var.get().strip()
            angle = self.manual_angle_var.get().strip()

            # Validate inputs
            if not keyword:
                messagebox.showerror("Validation Error", "Please enter a keyword/title.")
                return

            if not craft:
                messagebox.showerror("Validation Error", "Please select a craft.")
                return

            if not pillar:
                messagebox.showerror("Validation Error", "Please select a pillar.")
                return

            # Generate keyword vector embedding
            keyword_vector = None
            try:
                # Use the idea generator's embedding model
                if hasattr(self.idea_generator, 'embedding_model') and self.idea_generator.embedding_model:
                    vector = self.idea_generator.embedding_model.encode(keyword)
                    keyword_vector = self.db.serialize_vector(vector)
                else:
                    self.log_message("Warning: No embedding model available, adding without vector", "warning")
            except Exception as e:
                self.log_message(f"Warning: Could not generate embedding for keyword: {e}", "warning")

            # Insert into database
            content_id = self.db.insert_content_idea(
                keyword=keyword,
                pillar=pillar,
                craft=craft,
                proposed_angle=angle if angle else None,
                keyword_vector=keyword_vector
            )

            # Log success
            self.log_message(f"✅ User idea '{keyword}' added to Content Plan (ID: {content_id})", "success")

            # Clear input fields
            self.manual_keyword_var.set("")
            self.manual_craft_var.set("")
            self.manual_pillar_var.set("")
            self.manual_angle_var.set("")

            # Clear pillar dropdown
            self.manual_pillar_combo['values'] = []

            # Auto-calculate scores for new content, then refresh displays
            self.auto_calculate_scores()
            self.refresh_content_plan()
            self.update_dashboard_status()

        except Exception as e:
            self.log_message(f"❌ Error adding manual content idea: {e}", "error")
            messagebox.showerror("Error", f"Failed to add content idea: {e}")



    def refresh_content_plan(self):
        """Refresh the content plan TreeView."""
        try:
            # Delegate to content plan tab
            if hasattr(self, 'content_plan_tab'):
                self.content_plan_tab.refresh_content_plan()

        except Exception as e:
            self.log_message(f"Error refreshing content plan: {e}", "error")

    def prioritize_selected(self):
        """Set the selected content idea to highest priority."""
        content_id = self.content_plan_tab.get_selected_content_id()
        if content_id is None:
            messagebox.showwarning("No Selection", "Please select a content idea to prioritize.")
            return

        try:
            # 1. Database Operation: Set freshness score to 999 to ensure it runs next
            self.db.update_freshness_score(content_id, 999.0)

            # 2. Log the Action
            self.log_message(f"⭐ Prioritized content ID {content_id}", "success")
            
            # 3. Update "Next Job Candidate" Logic FIRST (recalculates scores and identifies new top candidate)
            self.update_next_job_info()
            
            # 4. Refresh Content Plan TreeView (uses updated next_job_candidate_id for highlighting)
            self.content_plan_tab.refresh_content_plan()
            
            # 5. Update Dashboard Statistics
            self.update_dashboard_status()

        except Exception as e:
            self.log_message(f"Error prioritizing content: {e}", "error")

    def assign_prompt_profile_to_selected(self, content_id: int, profile_id: Optional[int]):
        """
        Assign a prompt profile to a specific content item.

        Args:
            content_id: The ID of the content item
            profile_id: The ID of the prompt profile to assign (None to revert to default)
        """
        try:
            # Get profile name for logging
            if profile_id:
                profile = self.db.get_prompt_profile(profile_id)
                profile_name = profile['profile_name'] if profile else f"Profile ID {profile_id}"
            else:
                profile_name = "Default"

            # Update the content item
            success = self.db.update_content_prompt_profile(content_id, profile_id)

            if success:
                self.log_message(f"✅ Assigned '{profile_name}' profile to content ID {content_id}", "success")

                # Refresh UI components
                self.refresh_content_plan()
                self.update_dashboard_status()
            else:
                self.log_message(f"❌ Failed to assign profile to content ID {content_id}", "error")

        except Exception as e:
            self.log_message(f"Error assigning prompt profile: {e}", "error")

    def veto_selected(self):
        """Mark the selected content idea as rejected."""
        content_id = self.content_plan_tab.get_selected_content_id()
        if content_id is None:
            messagebox.showwarning("No Selection", "Please select a content idea to veto.")
            return

        try:
            # Confirm the action
            if messagebox.askyesno("Confirm Veto", "Are you sure you want to reject this content idea?"):
                # 1. Database Operation: Update status to rejected
                self.db.update_content_status(content_id, 'REJECTED_USER')
                
                # 2. Log the Action
                self.log_message(f"❌ Vetoed content ID {content_id}", "info")
                
                # 3. Update "Next Job Candidate" Logic FIRST (recalculates scores and identifies new top candidate)
                self.update_next_job_info()
                
                # 4. Refresh Content Plan TreeView (uses updated next_job_candidate_id for highlighting)
                self.content_plan_tab.refresh_content_plan()
                
                # 5. Update Dashboard Statistics
                self.update_dashboard_status()

        except Exception as e:
            self.log_message(f"Error vetoing content: {e}", "error")

    def delete_selected(self):
        """Delete the selected content idea from the database."""
        content_id = self.content_plan_tab.get_selected_content_id()
        if content_id is None:
            messagebox.showwarning("No Selection", "Please select a content idea to delete.")
            return

        try:
            # Get the content details for confirmation
            content = self.db.get_content_by_id(content_id)
            if not content:
                messagebox.showerror("Error", "Content not found in database.")
                return
            
            keyword = content.get('keyword', 'Unknown')
            
            # Confirm the deletion
            if messagebox.askyesno("Confirm Deletion", 
                                 f"Are you sure you want to permanently delete:\n\n'{keyword}'\n\nThis action cannot be undone."):
                # 1. Database Operation: Delete the content
                success = self.db.delete_content(content_id)
                if success:
                    # 2. Log the Action
                    self.log_message(f"🗑️ Deleted content: '{keyword}' (ID: {content_id})", "info")
                    
                    # 3. Update "Next Job Candidate" Logic FIRST (recalculates scores and identifies new top candidate)
                    self.update_next_job_info()
                    
                    # 4. Refresh Content Plan TreeView (uses updated next_job_candidate_id for highlighting)
                    self.content_plan_tab.refresh_content_plan()
                    
                    # 5. Update Dashboard Statistics
                    self.update_dashboard_status()
                else:
                    messagebox.showerror("Error", "Failed to delete content from database.")

        except Exception as e:
            self.log_message(f"Error deleting content: {e}", "error")
            messagebox.showerror("Error", f"Failed to delete content: {e}")

    def open_cleanup_dialog(self):
        """Open a dialog for database cleanup operations."""
        cleanup_window = tk.Toplevel(self.master)
        cleanup_window.title("Database Cleanup")
        cleanup_window.geometry("500x400")
        cleanup_window.resizable(False, False)
        
        # Make it modal
        cleanup_window.transient(self.master)
        cleanup_window.grab_set()
        
        # Center the window
        cleanup_window.update_idletasks()
        x = (cleanup_window.winfo_screenwidth() // 2) - (500 // 2)
        y = (cleanup_window.winfo_screenheight() // 2) - (400 // 2)
        cleanup_window.geometry(f"500x400+{x}+{y}")
        
        main_frame = ttk.Frame(cleanup_window, padding="15")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Title
        title_label = ttk.Label(main_frame, text="Database Cleanup Operations", 
                               font=("Arial", 14, "bold"))
        title_label.pack(pady=(0, 15))
        
        # Get current stats
        stats = self.db.get_content_stats()
        total = sum(stats.values())
        
        # Current stats display
        stats_frame = ttk.LabelFrame(main_frame, text="Current Database Statistics", padding="10")
        stats_frame.pack(fill=tk.X, pady=(0, 15))
        
        stats_text = "\n".join([f"{status}: {count}" for status, count in stats.items()])
        stats_text += f"\n\nTotal: {total} content ideas"
        ttk.Label(stats_frame, text=stats_text).pack()
        
        # Cleanup operations
        ops_frame = ttk.LabelFrame(main_frame, text="Cleanup Operations", padding="10")
        ops_frame.pack(fill=tk.X, pady=(0, 15))
        
        # Delete rejected content
        if stats.get('REJECTED_USER', 0) > 0:
            rejected_frame = ttk.Frame(ops_frame)
            rejected_frame.pack(fill=tk.X, pady=5)
            ttk.Label(rejected_frame, 
                     text=f"Delete {stats['REJECTED_USER']} rejected ideas:").pack(side=tk.LEFT)
            ttk.Button(rejected_frame, text="🗑️ Delete Rejected", 
                      command=lambda: self.cleanup_by_status('REJECTED_USER', cleanup_window)).pack(side=tk.RIGHT)
        
        # Delete old published content
        if stats.get('PUBLISHED', 0) > 0:
            published_frame = ttk.Frame(ops_frame)
            published_frame.pack(fill=tk.X, pady=5)
            ttk.Label(published_frame, 
                     text=f"Delete old published content (30+ days):").pack(side=tk.LEFT)
            ttk.Button(published_frame, text="🗑️ Delete Old Published", 
                      command=lambda: self.cleanup_old_published(cleanup_window)).pack(side=tk.RIGHT)
        
        # Reset all scores
        scores_frame = ttk.Frame(ops_frame)
        scores_frame.pack(fill=tk.X, pady=5)
        ttk.Label(scores_frame, text="Reset all freshness scores to 0:").pack(side=tk.LEFT)
        ttk.Button(scores_frame, text="🔄 Reset Scores", 
                  command=lambda: self.reset_all_scores(cleanup_window)).pack(side=tk.RIGHT)
        
        # Dangerous operations
        danger_frame = ttk.LabelFrame(main_frame, text="⚠️ Dangerous Operations", padding="10")
        danger_frame.pack(fill=tk.X, pady=(0, 15))
        
        # Delete all content
        danger_ops = ttk.Frame(danger_frame)
        danger_ops.pack(fill=tk.X, pady=5)
        ttk.Label(danger_ops, text="DELETE ALL CONTENT (cannot be undone):", 
                 foreground="red").pack(side=tk.LEFT)
        ttk.Button(danger_ops, text="💥 DELETE ALL", 
                  command=lambda: self.delete_all_content(cleanup_window)).pack(side=tk.RIGHT)
        
    def open_dangerous_operations_dialog(self):
        """Open a focused dialog for dangerous operations."""
        danger_window = tk.Toplevel(self.master)
        danger_window.title("⚠️ DANGEROUS OPERATIONS ⚠️")
        danger_window.geometry("400x300")
        danger_window.resizable(False, False)
        
        # Make it modal
        danger_window.transient(self.master)
        danger_window.grab_set()
        
        # Center the window
        danger_window.update_idletasks()
        x = (danger_window.winfo_screenwidth() // 2) - (400 // 2)
        y = (danger_window.winfo_screenheight() // 2) - (300 // 2)
        danger_window.geometry(f"400x300+{x}+{y}")
        
        main_frame = ttk.Frame(danger_window, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Warning title
        title_label = ttk.Label(main_frame, text="⚠️ DANGEROUS OPERATIONS ⚠️", 
                               font=("Arial", 16, "bold"), foreground="red")
        title_label.pack(pady=(0, 15))
        
        # Warning text
        warning_text = """WARNING: These operations cannot be undone!

These operations will permanently modify or delete data from your database. Use with extreme caution."""
        
        warning_label = ttk.Label(main_frame, text=warning_text, 
                                 font=("Arial", 10), justify=tk.CENTER, foreground="orange")
        warning_label.pack(pady=(0, 20))
        
        # Get current stats for display
        stats = self.db.get_content_stats()
        total = sum(stats.values())
        
        # Stats display
        stats_frame = ttk.LabelFrame(main_frame, text="Current Database", padding="10")
        stats_frame.pack(fill=tk.X, pady=(0, 15))
        
        stats_text = f"Total Content Items: {total}"
        ttk.Label(stats_frame, text=stats_text, font=("Arial", 10, "bold")).pack()
        
        # Dangerous operations section
        ops_frame = ttk.LabelFrame(main_frame, text="Dangerous Operations", padding="10")
        ops_frame.pack(fill=tk.X, pady=(0, 15))
        
        # Delete ALL content button
        delete_all_frame = ttk.Frame(ops_frame)
        delete_all_frame.pack(fill=tk.X, pady=5)
        
        ttk.Label(delete_all_frame, text="Delete ALL content:", foreground="red").pack(side=tk.LEFT)
        ttk.Button(delete_all_frame, text="💥 DELETE EVERYTHING", 
                  command=lambda: self.delete_all_content_dangerous(danger_window)).pack(side=tk.RIGHT)
        
        # Close button
        ttk.Button(main_frame, text="Close", 
                  command=danger_window.destroy).pack(pady=15)

    def delete_all_content_dangerous(self, parent_window):
        """Delete ALL content from the database with enhanced confirmations."""
        try:
            # Triple confirmation for dangerous operation
            if messagebox.askyesno("⚠️ DANGER ⚠️", 
                                 "This will DELETE ALL CONTENT from your database!\n\nAre you ABSOLUTELY sure?",
                                 parent=parent_window):
                if messagebox.askyesno("⚠️ FINAL WARNING ⚠️", 
                                     "This action cannot be undone!\n\nType 'DELETE ALL' in the next dialog to confirm.",
                                     parent=parent_window):
                    # Get confirmation text
                    confirm_text = simpledialog.askstring(
                        "Type DELETE ALL",
                        "Type exactly 'DELETE ALL' to confirm:",
                        parent=parent_window
                    )
                    
                    if confirm_text == "DELETE ALL":
                        # Get all content for counting
                        all_content = self.db.get_all_content()
                        total_count = len(all_content)
                        
                        # Delete all content
                        deleted = 0
                        for content in all_content:
                            if self.db.delete_content(content['id']):
                                deleted += 1
                        
                        self.log_message(f"💥 DANGEROUS OPERATION: Deleted ALL {deleted} content items from database", "warning")
                        messagebox.showinfo("Deletion Complete", 
                                           f"Deleted {deleted} of {total_count} content items.\n\nDatabase is now empty.", 
                                           parent=parent_window)
                        
                        # Refresh all displays
                        self.refresh_content_plan()
                        self.update_dashboard_status()
                        parent_window.destroy()
                    else:
                        messagebox.showinfo("Cancelled", "Deletion cancelled - text did not match.", parent=parent_window)
                        
        except Exception as e:
            self.log_message(f"Error during delete all operation: {e}", "error")
            messagebox.showerror("Error", f"Delete all failed: {e}", parent=parent_window)

    def auto_calculate_scores(self):
        """Delegate to dashboard tab."""
        if hasattr(self, 'dashboard_tab'):
            self.dashboard_tab.auto_calculate_scores()

    def refresh_all_scores(self):
        """Delegate to dashboard tab."""
        if hasattr(self, 'dashboard_tab'):
            self.dashboard_tab.refresh_all_scores()

    def generate_ideas(self):
        """Delegate to idea generator tab."""
        if hasattr(self, 'idea_generator_tab'):
            self.idea_generator_tab.generate_ideas()

    def open_dangerous_operations_dialog(self):
        """Open a focused dialog for dangerous operations."""
        danger_window = tk.Toplevel(self.master)
        danger_window.title("⚠️ DANGEROUS OPERATIONS ⚠️")
        danger_window.geometry("400x300")
        danger_window.resizable(False, False)
        
        # Make it modal
        danger_window.transient(self.master)
        danger_window.grab_set()
        
        # Center the window
        danger_window.update_idletasks()
        x = (danger_window.winfo_screenwidth() // 2) - (400 // 2)
        y = (danger_window.winfo_screenheight() // 2) - (300 // 2)
        danger_window.geometry(f"400x300+{x}+{y}")
        
        main_frame = ttk.Frame(danger_window, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Warning title
        title_label = ttk.Label(main_frame, text="⚠️ DANGEROUS OPERATIONS ⚠️", 
                               font=("Arial", 16, "bold"), foreground="red")
        title_label.pack(pady=(0, 15))
        
        # Warning text
        warning_text = """WARNING: These operations cannot be undone!

These operations will permanently modify or delete data from your database. Use with extreme caution."""
        
        warning_label = ttk.Label(main_frame, text=warning_text, 
                                 font=("Arial", 10), justify=tk.CENTER, foreground="orange")
        warning_label.pack(pady=(0, 20))
        
        # Get current stats for display
        stats = self.db.get_content_stats()
        total = sum(stats.values())
        
        # Stats display
        stats_frame = ttk.LabelFrame(main_frame, text="Current Database", padding="10")
        stats_frame.pack(fill=tk.X, pady=(0, 15))
        
        stats_text = f"Total Content Items: {total}"
        ttk.Label(stats_frame, text=stats_text, font=("Arial", 10, "bold")).pack()
        
        # Dangerous operations section
        ops_frame = ttk.LabelFrame(main_frame, text="Dangerous Operations", padding="10")
        ops_frame.pack(fill=tk.X, pady=(0, 15))
        
        # Delete ALL content button
        delete_all_frame = ttk.Frame(ops_frame)
        delete_all_frame.pack(fill=tk.X, pady=5)
        
        ttk.Label(delete_all_frame, text="Delete ALL content:", foreground="red").pack(side=tk.LEFT)
        ttk.Button(delete_all_frame, text="💥 DELETE EVERYTHING", 
                  command=lambda: self.delete_all_content_dangerous(danger_window)).pack(side=tk.RIGHT)
        
        # Close button
        ttk.Button(main_frame, text="Close", 
                  command=danger_window.destroy).pack(pady=15)

    def delete_all_content_dangerous(self, parent_window):
        """Delete ALL content from the database with enhanced confirmations."""
        try:
            # Triple confirmation for dangerous operation
            if messagebox.askyesno("⚠️ DANGER ⚠️", 
                                 "This will DELETE ALL CONTENT from your database!\n\nAre you ABSOLUTELY sure?",
                                 parent=parent_window):
                if messagebox.askyesno("⚠️ FINAL WARNING ⚠️", 
                                     "This action cannot be undone!\n\nType 'DELETE ALL' in the next dialog to confirm.",
                                     parent=parent_window):
                    # Get confirmation text
                    confirm_text = simpledialog.askstring(
                        "Type DELETE ALL",
                        "Type exactly 'DELETE ALL' to confirm:",
                        parent=parent_window
                    )
                    
                    if confirm_text == "DELETE ALL":
                        # Get all content for counting
                        all_content = self.db.get_all_content()
                        total_count = len(all_content)
                        
                        # Delete all content
                        deleted = 0
                        for content in all_content:
                            if self.db.delete_content(content['id']):
                                deleted += 1
                        
                        self.log_message(f"💥 DANGEROUS OPERATION: Deleted ALL {deleted} content items from database", "warning")
                        messagebox.showinfo("Deletion Complete", 
                                           f"Deleted {deleted} of {total_count} content items.\n\nDatabase is now empty.", 
                                           parent=parent_window)
                        
                        # Refresh all displays
                        self.refresh_content_plan()
                        self.update_dashboard_status()
                        parent_window.destroy()
                    else:
                        messagebox.showinfo("Cancelled", "Deletion cancelled - text did not match.", parent=parent_window)
    
        except Exception as e:
            self.log_message(f"Error in delete operation: {e}", "error")
            messagebox.showerror("Error", f"Failed to complete operation: {e}", parent=parent_window)

    def cleanup_by_status(self, status: str, parent_window):
        """Delete all content with a specific status."""
        try:
            content_list = self.db.get_content_by_status(status)
            count = len(content_list)
            
            if count == 0:
                messagebox.showinfo("No Content", f"No content found with status '{status}'.")
                return
            
            if messagebox.askyesno("Confirm Cleanup", 
                                 f"Delete {count} content ideas with status '{status}'?\n\nThis cannot be undone.",
                                 parent=parent_window):
                deleted = 0
                for content in content_list:
                    if self.db.delete_content(content['id']):
                        deleted += 1
                
                self.log_message(f"🧹 Cleaned up {deleted} content ideas with status '{status}'", "success")
                messagebox.showinfo("Cleanup Complete", f"Deleted {deleted} content ideas.", parent=parent_window)
                
                # Refresh displays
                self.refresh_content_plan()
                self.update_dashboard_status()
                parent_window.destroy()
                
        except Exception as e:
            self.log_message(f"Error during cleanup: {e}", "error")
            messagebox.showerror("Error", f"Cleanup failed: {e}", parent=parent_window)
    
    def cleanup_old_published(self, parent_window):
        """Delete published content older than 30 days."""
        try:
            # This would need to be implemented in the database module
            # For now, just show a message
            messagebox.showinfo("Feature Coming Soon", 
                              "Old published content cleanup will be implemented in a future version.",
                              parent=parent_window)
        except Exception as e:
            self.log_message(f"Error during old published cleanup: {e}", "error")
            messagebox.showerror("Error", f"Cleanup failed: {e}", parent=parent_window)
    
    def reset_all_scores(self, parent_window):
        """Reset all freshness scores to 0."""
        try:
            if messagebox.askyesno("Confirm Reset", 
                                 "Reset ALL freshness scores to 0?\n\nYou can recalculate them later.",
                                 parent=parent_window):
                # Get all content and reset scores
                all_content = self.db.get_all_content()
                updated = 0
                
                for content in all_content:
                    if self.db.update_freshness_score(content['id'], 0.0):
                        updated += 1
                
                self.log_message(f"🔄 Reset {updated} freshness scores", "success")
                messagebox.showinfo("Reset Complete", f"Reset {updated} freshness scores.", parent=parent_window)
                
                # Refresh displays
                self.refresh_content_plan()
                self.update_dashboard_status()
                parent_window.destroy()
                
        except Exception as e:
            self.log_message(f"Error resetting scores: {e}", "error")
            messagebox.showerror("Error", f"Reset failed: {e}", parent=parent_window)
    
    def delete_all_content(self, parent_window):
        """Delete ALL content from the database."""
        try:
            # Double confirmation for dangerous operation
            if messagebox.askyesno("⚠️ DANGER ⚠️", 
                                 "This will DELETE ALL CONTENT from your database!\n\nAre you ABSOLUTELY sure?",
                                 parent=parent_window):
                if messagebox.askyesno("⚠️ FINAL WARNING ⚠️", 
                                     "This action cannot be undone!\n\nType 'DELETE ALL' in the next dialog to confirm.",
                                     parent=parent_window):
                    # Get confirmation text
                    confirm_text = simpledialog.askstring(
                        "Type DELETE ALL",
                        "Type exactly 'DELETE ALL' to confirm:",
                        parent=parent_window
                    )
                    
                    if confirm_text == "DELETE ALL":
                        # Get all content for counting
                        all_content = self.db.get_all_content()
                        total_count = len(all_content)
                        
                        # Delete all content
                        deleted = 0
                        for content in all_content:
                            if self.db.delete_content(content['id']):
                                deleted += 1
                        
                        self.log_message(f"💥 DANGEROUS OPERATION: Deleted ALL {deleted} content items from database", "warning")
                        messagebox.showinfo("Deletion Complete", 
                                           f"Deleted {deleted} of {total_count} content items.\n\nDatabase is now empty.", 
                                           parent=parent_window)
                        
                        # Refresh all displays
                        self.refresh_content_plan()
                        self.update_dashboard_status()
                        parent_window.destroy()
                    else:
                        messagebox.showinfo("Cancelled", "Deletion cancelled - text did not match.", parent=parent_window)
                        
        except Exception as e:
            self.log_message(f"Error during delete all operation: {e}", "error")
            messagebox.showerror("Error", f"Delete all failed: {e}", parent=parent_window)

    def open_dangerous_operations_dialog(self):
        """Open a focused dialog for dangerous operations."""
        danger_window = tk.Toplevel(self.master)
        danger_window.title("⚠️ DANGEROUS OPERATIONS ⚠️")
        danger_window.geometry("400x300")
        danger_window.resizable(False, False)
        
        # Make it modal
        danger_window.transient(self.master)
        danger_window.grab_set()
        
        # Center the window
        danger_window.update_idletasks()
        x = (danger_window.winfo_screenwidth() // 2) - (400 // 2)
        y = (danger_window.winfo_screenheight() // 2) - (300 // 2)
        danger_window.geometry(f"400x300+{x}+{y}")
        
        main_frame = ttk.Frame(danger_window, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Warning title
        title_label = ttk.Label(main_frame, text="⚠️ DANGEROUS OPERATIONS ⚠️", 
                               font=("Arial", 16, "bold"), foreground="red")
        title_label.pack(pady=(0, 15))
        
        # Warning text
        warning_text = """WARNING: These operations cannot be undone!

These operations will permanently modify or delete data from your database. Use with extreme caution."""
        
        warning_label = ttk.Label(main_frame, text=warning_text, 
                                 font=("Arial", 10), justify=tk.CENTER, foreground="orange")
        warning_label.pack(pady=(0, 20))
        
        # Get current stats for display
        stats = self.db.get_content_stats()
        total = sum(stats.values())
        
        # Stats display
        stats_frame = ttk.LabelFrame(main_frame, text="Current Database", padding="10")
        stats_frame.pack(fill=tk.X, pady=(0, 15))
        
        stats_text = f"Total Content Items: {total}"
        ttk.Label(stats_frame, text=stats_text, font=("Arial", 10, "bold")).pack()
        
        # Dangerous operations section
        ops_frame = ttk.LabelFrame(main_frame, text="Dangerous Operations", padding="10")
        ops_frame.pack(fill=tk.X, pady=(0, 15))
        
        # Delete ALL content button
        delete_all_frame = ttk.Frame(ops_frame)
        delete_all_frame.pack(fill=tk.X, pady=5)
        
        ttk.Label(delete_all_frame, text="Delete ALL content:", foreground="red").pack(side=tk.LEFT)
        ttk.Button(delete_all_frame, text="💥 DELETE EVERYTHING", 
                  command=lambda: self.delete_all_content(danger_window)).pack(side=tk.RIGHT)
        
        # Close button
        ttk.Button(main_frame, text="Close", 
                  command=danger_window.destroy).pack(pady=15)

    def delete_all_content_dangerous(self, parent_window):
        """Delete ALL content from the database with enhanced confirmations."""
        try:
            # Triple confirmation for dangerous operation
            if messagebox.askyesno("⚠️ DANGER ⚠️", 
                                 "This will DELETE ALL CONTENT from your database!\n\nAre you ABSOLUTELY sure?",
                                 parent=parent_window):
                if messagebox.askyesno("⚠️ FINAL WARNING ⚠️", 
                                     "Last chance! This CANNOT be undone!\n\nDelete everything?",
                                     parent=parent_window):
                    all_content = self.db.get_all_content()
                    deleted = 0
                    
                    for content in all_content:
                        if self.db.delete_content(content['id']):
                            deleted += 1
                    
                    self.log_message(f"💥 DELETED ALL CONTENT - {deleted} ideas removed", "warning")
                    messagebox.showinfo("Database Cleared", f"Deleted {deleted} content ideas.\n\nDatabase is now empty.", 
                                       parent=parent_window)
                    
                    # Refresh displays
                    self.refresh_content_plan()
                    self.update_dashboard_status()
                    parent_window.destroy()
                    
        except Exception as e:
            self.log_message(f"Error deleting all content: {e}", "error")
            messagebox.showerror("Error", f"Delete all failed: {e}", parent=parent_window)

    def refresh_all_scores(self):
        """Refresh all freshness scores using current settings."""
        def refresh_thread():
            try:
                self.log_message("🔄 Refreshing all freshness scores...", "info")

                # Get current settings
                freshness_threshold = float(config_manager.get_pipeline_setting('freshness_threshold') or 70.0)
                pillar_weights_str = config_manager.get_pipeline_setting('pillar_weights') or '{}'
                try:
                    pillar_weights = json.loads(pillar_weights_str)
                except json.JSONDecodeError:
                    pillar_weights = {}  # Fallback to empty dict

                # 1. Calculate scores (updates DB)
                scored_content = self.planner.calculate_freshness_scores(pillar_weights)

                self.log_message(f"✅ Refreshed scores for {len(scored_content)} content ideas", "success")
                
                # Debug: Log a few scores to verify they're calculated
                if scored_content:
                    self.log_message("Top 3 calculated scores:", "info")
                    for i, content in enumerate(scored_content[:3]):
                        keyword = content.get('keyword', 'Unknown')
                        score = content.get('freshness_score', 0)
                        self.log_message(f"  {i+1}. '{keyword}': {score:.1f}", "info")

                # Force refresh displays with correct sequence after database updates are committed
                import time
                time.sleep(0.1)  # Small delay to ensure DB updates are committed
                
                # 2. Update "Next Job Candidate" Logic FIRST (recalculates scores and identifies new top candidate)
                self.master.after(100, self.update_next_job_info)
                
                # 3. Refresh Content Plan TreeView (uses updated next_job_candidate_id for highlighting)
                self.master.after(200, self.refresh_content_plan)
                
                # 4. Update Dashboard Statistics
                self.master.after(300, self.update_dashboard_status)

            except Exception as e:
                self.log_message(f"❌ Error refreshing scores: {e}", "error")
                import traceback
                print(f"Full traceback: {traceback.format_exc()}")

        threading.Thread(target=refresh_thread, daemon=True).start()

    def auto_calculate_scores(self):
        """Automatically calculate freshness scores with default settings."""
        try:
            # Get default settings for scoring
            pillar_weights_str = config_manager.get_pipeline_setting('pillar_weights') or '{}'
            try:
                pillar_weights = json.loads(pillar_weights_str)
            except json.JSONDecodeError:
                # If no valid pillar weights, create some basic ones
                business_pillars = config_manager.get_pipeline_setting('business_pillars') or ''
                pillar_weights = {}
                for line in business_pillars.split('\n'):
                    line = line.strip()
                    if '=' in line:
                        craft = line.split('=', 1)[0].strip()
                        if craft:
                            pillar_weights[craft] = 1.0  # Default weight
            
            # Calculate scores silently in background
            def score_thread():
                try:
                    scored_content = self.planner.calculate_freshness_scores(pillar_weights)
                    self.log_message(f"📊 Auto-calculated scores for {len(scored_content)} content ideas", "info")
                    
                    # Refresh the content plan to show updated scores
                    self.master.after(0, self.refresh_content_plan)
                except Exception as e:
                    self.log_message(f"Error auto-calculating scores: {e}", "warning")
            
            threading.Thread(target=score_thread, daemon=True).start()
            
        except Exception as e:
            self.log_message(f"Error starting auto-calculation: {e}", "error")

    def open_settings(self):
        """Open the settings window and refresh IdeaGeneratorTab afterwards."""
        try:
            from gui.settings_window import SettingsWindow
            settings_window_top = tk.Toplevel(self.master)
            settings_window = SettingsWindow(settings_window_top)
            settings_window_top.wait_window()  # Wait for settings window to close
            
            # After settings window closes, refresh the IdeaGeneratorTab displays
            if hasattr(self, 'idea_generator_tab'):
                self.idea_generator_tab.load_generation_options()
                
        except Exception as e:
            self.log_message(f"Error opening settings: {e}", "error")
            
    def show_about(self):
        """Show the About dialog with application information and PLANNED status explanation."""
        try:
            about_window = tk.Toplevel(self.master)
            about_window.title("About Content Strategist")
            about_window.geometry("600x500")
            about_window.resizable(False, False)
            about_window.transient(self.master)
            about_window.grab_set()
            
            # Center the window
            about_window.update_idletasks()
            x = (about_window.winfo_screenwidth() // 2) - (600 // 2)
            y = (about_window.winfo_screenheight() // 2) - (500 // 2)
            about_window.geometry(f"600x500+{x}+{y}")
            
            main_frame = ttk.Frame(about_window, padding="20")
            main_frame.pack(fill=tk.BOTH, expand=True)
            
            # Title
            title_label = ttk.Label(main_frame, text="Content Strategist Dashboard", 
                                   font=("Arial", 16, "bold"))
            title_label.pack(pady=(0, 10))
            
            # Version info
            version_label = ttk.Label(main_frame, text="Advanced SEO Content Management System", 
                                     font=("Arial", 10))
            version_label.pack(pady=(0, 20))
            
            # Create notebook for tabs
            about_notebook = ttk.Notebook(main_frame)
            about_notebook.pack(fill=tk.BOTH, expand=True, pady=(0, 15))
            
            # Overview tab
            overview_frame = ttk.Frame(about_notebook)
            about_notebook.add(overview_frame, text="Overview")
            
            overview_text = tk.Text(overview_frame, wrap=tk.WORD, state=tk.DISABLED)
            overview_text.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
            
            overview_content = """Content Strategist Dashboard

This application helps you manage your SEO content pipeline from idea generation to publication. 

Key Features:
• Automated content idea generation using advanced LLMs
• Intelligent freshness scoring and prioritization
• Multi-platform publishing (Shopify, WordPress)
• Comprehensive analytics and tracking
• Flexible workflow management

The system uses a sophisticated scoring algorithm to identify the best content opportunities and streamline your content creation process."""
            
            overview_text.config(state=tk.NORMAL)
            overview_text.insert('1.0', overview_content)
            overview_text.config(state=tk.DISABLED)
            
            # Status Guide tab
            status_frame = ttk.Frame(about_notebook)
            about_notebook.add(status_frame, text="Status Guide")
            
            status_text = tk.Text(status_frame, wrap=tk.WORD, state=tk.DISABLED)
            status_text.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
            
            status_content = """Content Status Guide

NEW: Freshly generated content ideas that haven't been scored yet.

ON_HOLD: Ideas that have been scored but don't meet the current threshold for automatic planning.

PLANNED: This idea has been marked as ready to be worked on. It can be selected for immediate execution by:
  • Being the latest item chosen by the '🔎 Plan Next Job' button (automatic selection)
  • Being manually primed for execution via the right-click '🎯 Prime This for Execution' option in the Content Plan tab

Once primed, the job details appear in the 'Job Primed for Execution' area on the Dashboard, and you can click '🚀 Execute Job' to run the content generation process.

Note: Multiple items can have PLANNED status, but only one can be "primed" for execution at a time.

IN_PROGRESS: Currently being processed by the system.

PUBLISHED: Successfully completed and published to your platform.

REJECTED_USER: Manually vetoed ideas that won't be processed.

REJECTED_SYSTEM: Ideas automatically rejected by quality filters."""
            
            status_text.config(state=tk.NORMAL)
            status_text.insert('1.0', status_content)
            status_text.config(state=tk.DISABLED)
            
            # Workflow tab
            workflow_frame = ttk.Frame(about_notebook)
            about_notebook.add(workflow_frame, text="Workflow")
            
            workflow_text = tk.Text(workflow_frame, wrap=tk.WORD, state=tk.DISABLED)
            workflow_text.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
            
            workflow_content = """Recommended Workflow

1. Generate Ideas:
   • Use the Idea Generator tab to create new content concepts
   • Ideas start with 'NEW' status

2. Score and Plan:
   • Auto-calculate scores to evaluate content potential
   • Use '🔎 Plan Next Job' to automatically select the best candidate
   • OR manually select any idea and use '🎯 Prime This for Execution'

3. Execute Content:
   • Configure execution options (data sources, LLM, platform)
   • Click '🚀 Execute Job' to generate and publish content

4. Manage and Track:
   • Monitor progress in the Content Plan tab
   • Use filtering and search to find specific ideas
   • Manually prioritize or veto ideas as needed

Tips:
• Green highlighting shows the next auto-candidate
• Blue highlighting shows planned items
• Right-click any item for quick actions"""
            
            workflow_text.config(state=tk.NORMAL)
            workflow_text.insert('1.0', workflow_content)
            workflow_text.config(state=tk.DISABLED)
            
            # Close button
            ttk.Button(main_frame, text="Close", command=about_window.destroy).pack(pady=10)
            
        except Exception as e:
            self.log_message(f"Error showing about dialog: {e}", "error")
            messagebox.showerror("Error", f"Failed to display about dialog: {e}")
    
    def show_database_stats(self):
        """Show detailed database statistics."""
        try:
            stats = self.db.get_content_stats()
            total = sum(stats.values())
            
            stats_text = "Database Statistics:\n\n"
            for status, count in stats.items():
                percentage = (count / total * 100) if total > 0 else 0
                stats_text += f"{status}: {count} ({percentage:.1f}%)\n"
            stats_text += f"\nTotal Content Ideas: {total}"
            
            messagebox.showinfo("Database Statistics", stats_text)
            
        except Exception as e:
            self.log_message(f"Error showing database stats: {e}", "error")
            messagebox.showerror("Error", f"Failed to show database statistics: {e}")
    
    def show_scoring_debug(self):
        """Show scoring debug information."""
        try:
            # Get current settings
            freshness_threshold = float(config_manager.get_pipeline_setting('freshness_threshold') or 70.0)
            pillar_weights_str = config_manager.get_pipeline_setting('pillar_weights') or '{}'
            
            debug_text = f"""Scoring Debug Information

Freshness Threshold: {freshness_threshold}
Pillar Weights: {pillar_weights_str}

This information helps debug the scoring algorithm.
For detailed score calculation, check the Log tab during score refresh."""
            
            messagebox.showinfo("Scoring Debug Info", debug_text)
            
        except Exception as e:
            self.log_message(f"Error showing scoring debug: {e}", "error")
            messagebox.showerror("Error", f"Failed to show scoring debug: {e}")
    
    def show_embedding_status(self):
        """Show embedding model status."""
        try:
            has_embedding = hasattr(self.idea_generator, 'embedding_model') and self.idea_generator.embedding_model
            
            status_text = f"""Embedding Model Status

Embedding Model Available: {'Yes' if has_embedding else 'No'}

Embeddings are used for:
• Keyword similarity analysis
• Content idea clustering
• Improved content scoring

If no embedding model is available, the system will still work but with reduced accuracy."""
            
            messagebox.showinfo("Embedding Status", status_text)
            
        except Exception as e:
            self.log_message(f"Error showing embedding status: {e}", "error")
            messagebox.showerror("Error", f"Failed to show embedding status: {e}")
    
    def force_refresh_gui(self):
        """Force refresh all GUI elements."""
        try:
            self.log_message("🔄 Force refreshing GUI...", "info")
            
            # Update all major components
            self.update_next_job_info()
            self.refresh_content_plan()
            self.update_dashboard_status()
            self.populate_llm_dropdowns()
            
            self.log_message("✅ GUI refresh completed", "success")
            
        except Exception as e:
            self.log_message(f"Error force refreshing GUI: {e}", "error")
            messagebox.showerror("Error", f"Failed to refresh GUI: {e}")
    
    def export_to_csv(self):
        """Export content data to CSV file."""
        try:
            filename = filedialog.asksaveasfilename(
                title="Export Content Plan to CSV",
                defaultextension=".csv",
                filetypes=[("CSV files", "*.csv"), ("All files", "*.*")],
                initialname=f"content_plan_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
            )
            
            if filename:
                import csv
                all_content = self.db.get_all_content()
                
                with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
                    if all_content:
                        fieldnames = all_content[0].keys()
                        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                        writer.writeheader()
                        
                        for content in all_content:
                            # Handle binary data that might be in the content
                            clean_content = {}
                            for key, value in content.items():
                                if isinstance(value, bytes):
                                    clean_content[key] = f"<binary data: {len(value)} bytes>"
                                else:
                                    clean_content[key] = value
                            writer.writerow(clean_content)
                
                self.log_message(f"💾 Exported {len(all_content)} items to: {filename}", "success")
                messagebox.showinfo("Export Complete", f"Exported {len(all_content)} content ideas to:\n{filename}")
            
        except Exception as e:
            self.log_message(f"Error exporting to CSV: {e}", "error")
            messagebox.showerror("Error", f"Failed to export to CSV: {e}")
    
    def open_settings(self):
        """Open the settings window."""
        try:
            settings_window = SettingsWindow(self.master)
            settings_window.transient(self.master)
            settings_window.grab_set()
            
        except Exception as e:
            self.log_message(f"Error opening settings: {e}", "error")
            messagebox.showerror("Error", f"Failed to open settings: {e}")
    
    def on_closing(self):
        """Handle application closing."""
        try:
            if self.is_running:
                if messagebox.askyesno("Confirm Exit", "An operation is currently running. Do you want to force quit?"):
                    self.stop_requested = True
                else:
                    return
            
            self.master.destroy()
            
        except Exception as e:
            self.log_message(f"Error during closing: {e}", "error")
            self.master.destroy()
    
    def clear_log(self):
        """Clear the log text area."""
        try:
            self.log_text.config(state=tk.NORMAL)
            self.log_text.delete('1.0', tk.END)
            self.log_text.config(state=tk.DISABLED)
            self.log_message("Log cleared", "info")
            
        except Exception as e:
            print(f"Error clearing log: {e}")
    
    def load_generation_options(self):
        """Load creativity vectors and crafts for idea generation."""
        try:
            # Get business pillars from config
            business_pillars = config_manager.get_pipeline_setting('business_pillars') or ""
            
            crafts = []
            for line in business_pillars.split('\n'):
                line = line.strip()
                if '=' in line:
                    craft = line.split('=', 1)[0].strip()
                    if craft:
                        crafts.append(craft)
            
            # Update craft dropdown
            self.craft_combo['values'] = crafts
            
            # Set some default creativity vectors
            vectors = [
                "Controversial",
                "Beginner-friendly", 
                "Expert-level",
                "Trending",
                "Evergreen",
                "Problem-solving",
                "Comparison",
                "Tutorial"
            ]
            self.vector_combo['values'] = vectors
            
        except Exception as e:
            self.log_message(f"Error loading generation options: {e}", "error")
    
    def generate_ideas(self):
        """Generate new content ideas using the idea generator."""
        try:
            messagebox.showinfo("Feature Coming Soon", "Idea generation will be implemented in a future version.")
            
        except Exception as e:
            self.log_message(f"Error generating ideas: {e}", "error")
    
    def open_database_file(self):
        """Open the database file location in file explorer."""
        try:
            import os
            import subprocess
            import platform
            
            db_path = self.db.db_path
            db_dir = os.path.dirname(db_path)
            
            # Show info about the database
            stats = self.db.get_content_stats()
            total = sum(stats.values())
            
            info_text = f"""Database Information:

File Location: {db_path}
Total Content Ideas: {total}

You can:
• Use DB Browser for SQLite to view/edit the database
• Back up the .db file to save your content
• Delete the .db file to start fresh (will recreate automatically)

Open the database folder?"""
            
            if messagebox.askyesno("Database Access", info_text):
                # Open the folder containing the database
                if platform.system() == "Windows":
                    os.startfile(db_dir)
                elif platform.system() == "Darwin":  # macOS
                    subprocess.run(["open", db_dir])
                else:  # Linux
                    subprocess.run(["xdg-open", db_dir])
                    
                self.log_message(f"📁 Opened database folder: {db_dir}", "info")
                
        except Exception as e:
            self.log_message(f"Error opening database folder: {e}", "error")
            messagebox.showerror("Error", f"Could not open database folder: {e}")

    def export_to_csv(self):
        """Export all content to a CSV file for easy viewing/editing."""
        try:
            from tkinter import filedialog
            import csv
            import os
            from datetime import datetime
            
            # Get all content
            all_content = self.db.get_all_content()
            
            if not all_content:
                messagebox.showinfo("No Data", "No content found in database to export.")
                return
            
            # Default filename with timestamp
            default_filename = f"content_export_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
            
            # Ask user where to save
            filename = filedialog.asksaveasfilename(
                defaultextension=".csv",
                filetypes=[("CSV files", "*.csv"), ("All files", "*.*")],
                title="Export Content to CSV",
                initialvalue=default_filename
            )
            
            if not filename:
                return  # User cancelled
            
            # Export to CSV
            with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
                # Define fieldnames (excluding binary data)
                fieldnames = ['id', 'keyword', 'pillar', 'craft', 'status', 'proposed_angle', 
                             'freshness_score', 'platform_url', 'published_date', 'created_date', 'updated_date']
                
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                writer.writeheader()
                
                for content in all_content:
                    # Create a clean row without binary data
                    row = {}
                    for field in fieldnames:
                        value = content.get(field, '')
                        # Handle special cases
                        if field == 'freshness_score':
                            if isinstance(value, bytes):
                                value = 0.0
                            try:
                                value = float(value) if value else 0.0
                            except (ValueError, TypeError):
                                value = 0.0
                        elif value is None:
                            value = ''
                        row[field] = value
                    
                    writer.writerow(row)
            
            self.log_message(f"📊 Exported {len(all_content)} content ideas to {filename}", "success")
            messagebox.showinfo("Export Complete", 
                              f"Exported {len(all_content)} content ideas to:\n\n{filename}\n\nYou can now view and edit this in Excel or any spreadsheet program.")
            
        except Exception as e:
            self.log_message(f"Error exporting to CSV: {e}", "error")
            messagebox.showerror("Export Error", f"Failed to export content: {e}")

    def generate_ideas(self):
        """Generate new content ideas in a background thread."""
        if self.is_running:
            messagebox.showwarning("Operation Running", "An operation is already running. Please wait for it to complete.")
            return

        def generate_thread():
            try:
                # Set running state and update UI
                self.is_running = True
                self.stop_requested = False
                self.master.after(0, lambda: self.generate_ideas_button.config(state=tk.DISABLED, text="Generating Ideas..."))
                self.master.after(0, lambda: self.stop_button.config(state=tk.NORMAL))
                self.master.after(0, lambda: self.idea_progress.pack(side=tk.LEFT, padx=(0, 10)))
                self.master.after(0, lambda: self.idea_progress.start(10))

                self.log_message("💡 Generating new content ideas...", "info")

                # Check for stop request
                if self.stop_requested:
                    self.log_message("🛑 Idea generation stopped by user", "warning")
                    return

                # Get settings
                num_ideas = self.num_ideas_var.get()
                target_craft = self.target_craft_var.get() or None
                target_vector = self.target_vector_var.get() or None

                # Get business pillars from settings
                business_pillars = config_manager.get_pipeline_setting('business_pillars')
                if not business_pillars:
                    self.log_message("❌ No business pillars configured. Please set them in Settings > Business Logic", "error")
                    return

                # Check for stop request
                if self.stop_requested:
                    self.log_message("🛑 Idea generation stopped by user", "warning")
                    return

                # Get selected LLM settings for idea generation
                idea_provider = self.idea_gen_llm_provider_var.get()
                idea_model = self.idea_gen_llm_model_var.get()

                if idea_provider and idea_model:
                    self.log_message(f"Using {idea_provider} ({idea_model}) for idea generation", "info")
                    # Override the idea generator's LLM settings
                    original_provider = config_manager.get_pipeline_setting('analysis_llm')
                    original_model = config_manager.get_pipeline_setting('analysis_llm_model')

                    # Temporarily update config for this generation
                    config_manager.update_setting('PIPELINE_SETTINGS', 'analysis_llm', idea_provider)
                    config_manager.update_setting('PIPELINE_SETTINGS', 'analysis_llm_model', idea_model)

                try:
                    # Check for stop request before generating
                    if self.stop_requested:
                        self.log_message("🛑 Idea generation stopped by user", "warning")
                        return

                    # Generate ideas
                    ideas = self.idea_generator.generate_ideas(
                        pillars_text=business_pillars,
                        target_craft=target_craft,
                        target_creativity_vector=target_vector,
                        num_ideas=num_ideas
                    )

                    # Check for stop request after generation
                    if self.stop_requested:
                        self.log_message("🛑 Idea generation stopped by user", "warning")
                        return

                    # Display results
                    results_text = f"Generated {len(ideas)} new content ideas:\n\n"
                    for i, idea in enumerate(ideas, 1):
                        results_text += f"{i}. {idea['keyword']}\n"
                        results_text += f"   Pillar: {idea['pillar']} | Craft: {idea['craft']}\n"
                        results_text += f"   Angle: {idea['proposed_angle']}\n\n"

                    self.master.after(0, lambda: self.display_generated_ideas(results_text))
                    self.log_message(f"✅ Successfully generated {len(ideas)} new ideas", "success")

                    # Refresh displays
                    self.master.after(0, self.refresh_content_plan)
                    self.master.after(0, self.update_dashboard_status)

                finally:
                    # Restore original LLM settings if they were overridden
                    if idea_provider and idea_model:
                        if original_provider:
                            config_manager.update_setting('PIPELINE_SETTINGS', 'analysis_llm', original_provider)
                        if original_model:
                            config_manager.update_setting('PIPELINE_SETTINGS', 'analysis_llm_model', original_model)

            except Exception as e:
                self.log_message(f"❌ Error generating ideas: {e}", "error")
            finally:
                # Reset running state and UI
                self.is_running = False
                self.stop_requested = False
                self.master.after(0, lambda: self.generate_ideas_button.config(state=tk.NORMAL, text="💡 Generate New Ideas"))
                self.master.after(0, lambda: self.stop_button.config(state=tk.DISABLED))
                self.master.after(0, lambda: self.idea_progress.stop())
                self.master.after(0, lambda: self.idea_progress.pack_forget())

        threading.Thread(target=generate_thread, daemon=True).start()

    def display_generated_ideas(self, text: str):
        """Display generated ideas in the text area."""
        self.ideas_text.delete('1.0', tk.END)
        self.ideas_text.insert('1.0', text)

    def load_generation_options(self):
        """Load creativity vectors and crafts into the comboboxes."""
        try:
            # Load creativity vectors
            vectors = self.idea_generator.get_creativity_vectors()
            self.vector_combo['values'] = [''] + vectors

            # Load crafts from business pillars
            business_pillars = config_manager.get_pipeline_setting('business_pillars') or ''
            crafts = []
            for line in business_pillars.split('\n'):
                line = line.strip()
                if '=' in line:
                    craft = line.split('=', 1)[0].strip()
                    if craft:
                        crafts.append(craft)

            self.craft_combo['values'] = [''] + crafts

        except Exception as e:
            self.log_message(f"Error loading generation options: {e}", "error")



    def open_settings(self):
        """Open the settings window."""
        settings_window = tk.Toplevel(self.master)
        SettingsWindow(settings_window)

        # Refresh data after settings window closes
        settings_window.wait_window()
        self.load_generation_options()
        self.populate_llm_dropdowns()  # Refresh LLM dropdowns in case models were updated
        self.update_dashboard_status()

    def show_database_stats(self):
        """Show detailed database statistics."""
        try:
            stats = self.db.get_content_stats()
            total = sum(stats.values())

            stats_text = "Detailed Database Statistics:\n\n"
            for status, count in stats.items():
                percentage = (count / total * 100) if total > 0 else 0
                stats_text += f"{status}: {count} ({percentage:.1f}%)\n"

            stats_text += f"\nTotal Content Ideas: {total}"

            # Get queue summary
            queue_summary = self.planner.get_queue_summary()
            stats_text += f"\nContent in Queue: {queue_summary['total_in_queue']}"

            messagebox.showinfo("Database Statistics", stats_text)

        except Exception as e:
            self.log_message(f"Error getting database stats: {e}", "error")

    def show_about(self):
        """Show about dialog."""
        about_text = """Content Strategist Dashboard

A proactive, database-driven content planning and execution system.

Features:
• Strategic content planning with freshness scoring
• Automated content idea generation
• Complete content lifecycle management
• Direct publishing to Shopify and WordPress
• Real-time progress monitoring

Built with Python and powered by advanced LLM integration."""

        messagebox.showinfo("About Content Strategist", about_text)

    def clear_log(self):
        """Clear the log text area."""
        self.log_text.config(state=tk.NORMAL)
        self.log_text.delete('1.0', tk.END)
        self.log_text.config(state=tk.DISABLED)

    def force_refresh_gui(self):
        """Force refresh the GUI to debug display issues."""
        try:
            self.log_message("🔄 FORCE REFRESH: Starting GUI debug refresh...", "info")
            
            # Directly query database and log raw data
            self.log_message("🔍 Step 1: Querying database directly...", "info")
            all_content = self.db.get_all_content()
            
            self.log_message(f"🔍 Step 2: Retrieved {len(all_content)} items from database", "info")
            
            # Log raw database values for first few items
            for i, content in enumerate(all_content[:3]):
                keyword = content.get('keyword', 'Unknown')
                raw_score = content.get('freshness_score')
                self.log_message(f"  Raw DB item {i+1}: '{keyword}' = {repr(raw_score)} (type: {type(raw_score)})", "info")
            
            self.log_message("🔍 Step 3: Refreshing TreeView...", "info")
            
            # Clear and repopulate with verbose debugging
            for item in self.content_tree.get_children():
                self.content_tree.delete(item)
                
            # Re-populate with debug output
            self.populate_content_tree(all_content)
            
            self.log_message("✅ FORCE REFRESH: Complete", "success")
            
        except Exception as e:
            self.log_message(f"❌ FORCE REFRESH: Error - {e}", "error")
            import traceback
            print(f"Full traceback: {traceback.format_exc()}")
    
    def show_scoring_debug(self):
        """Show detailed scoring debug information."""
        try:
            debug_info = self.planner.get_scoring_debug_info()
            
            if not debug_info:
                messagebox.showinfo("Scoring Debug", "No scoring debug information available.\n\nRun 'Refresh All Scores' or 'Auto-Calculate Scores' first to generate debug data.")
                return
            
            # Create debug window
            debug_window = tk.Toplevel(self.master)
            debug_window.title("🔍 Freshness Scoring Debug Information")
            debug_window.geometry("800x600")
            debug_window.resizable(True, True)
            debug_window.transient(self.master)
            
            # Create main frame with scrolling
            main_frame = ttk.Frame(debug_window, padding="10")
            main_frame.pack(fill=tk.BOTH, expand=True)
            
            # Create scrolled text widget
            debug_text = scrolledtext.ScrolledText(main_frame, wrap=tk.WORD, font=("Consolas", 10))
            debug_text.pack(fill=tk.BOTH, expand=True)
            
            # Build debug information
            debug_content = "🔍 FRESHNESS SCORING DEBUG REPORT\n"
            debug_content += "=" * 50 + "\n\n"
            
            # Embedding status
            debug_content += f"🧠 EMBEDDING STATUS: {debug_info.get('embedding_status', 'Unknown')}\n\n"
            
            # Content counts
            debug_content += f"📊 CONTENT STATISTICS:\n"
            debug_content += f"   • Content scored: {debug_info.get('content_count', 0)}\n"
            debug_content += f"   • Published content for comparison: {debug_info.get('published_count', 0)}\n\n"
            
            # Scoring settings
            settings = debug_info.get('settings', {})
            debug_content += f"⚙️ SCORING SETTINGS:\n"
            debug_content += f"   • Time weight: {settings.get('time_weight', 'N/A')}\n"
            debug_content += f"   • Uniqueness weight: {settings.get('uniqueness_weight', 'N/A')}\n"
            debug_content += f"   • Max angle bonus: {settings.get('max_angle_bonus', 'N/A')}\n\n"
            
            # Pillar weights
            pillar_weights = debug_info.get('pillar_weights', {})
            debug_content += f"⚖️ PILLAR WEIGHTS:\n"
            if pillar_weights:
                for craft, weight in pillar_weights.items():
                    debug_content += f"   • {craft}: {weight}\n"
            else:
                debug_content += "   • No pillar weights configured\n"
            debug_content += "\n"
            
            # Zero scores warning
            zero_scores = debug_info.get('zero_scores', [])
            if zero_scores:
                debug_content += f"⚠️ ITEMS WITH 0.0 SCORES ({len(zero_scores)}):  \n"
                for keyword in zero_scores:
                    debug_content += f"   • {keyword}\n"
                debug_content += "\n"
                debug_content += "💡 TROUBLESHOOTING TIPS FOR 0.0 SCORES:\n"
                debug_content += "   1. Check if sentence-transformers is installed: pip install sentence-transformers\n"
                debug_content += "   2. Ensure keyword vectors are being generated (check embedding status above)\n"
                debug_content += "   3. Verify pillar weights are configured correctly\n"
                debug_content += "   4. Check if proposed angles are present (angle bonus = 0 if empty)\n"
                debug_content += "   5. Look at the Log tab for detailed per-item debug output\n\n"
            
            # Instructions
            debug_content += "📝 HOW TO USE THIS INFORMATION:\n"
            debug_content += "   • If embedding status shows 'Failed to load', install sentence-transformers\n"
            debug_content += "   • If many items have 0.0 scores, check embedding status and pillar weights\n"
            debug_content += "   • Detailed per-item scoring appears in the Log tab when debug mode is on\n"
            debug_content += "   • Adjust pillar weights in Settings > Business Logic to influence scoring\n\n"
            
            debug_content += "🔄 To get fresh debug data, use 'Tools > Refresh All Scores'\n"
            
            # Insert content and make read-only
            debug_text.insert('1.0', debug_content)
            debug_text.config(state=tk.DISABLED)
            
            # Add close button
            close_button = ttk.Button(main_frame, text="Close", command=debug_window.destroy)
            close_button.pack(pady=10)
            
        except Exception as e:
            self.log_message(f"Error showing scoring debug: {e}", "error")
            messagebox.showerror("Debug Error", f"Could not display debug information: {e}")

    def show_embedding_status(self):
        """Show embedding model status."""
        try:
            # Get actual embedding status from both idea generator and planner
            idea_gen_status = getattr(self.idea_generator, 'embedding_status', 'Unknown')
            planner_status = self.planner.get_embedding_status()
            
            # Create status window
            status_window = tk.Toplevel(self.master)
            status_window.title("🧠 Embedding Model Status")
            status_window.geometry("600x500")
            status_window.resizable(True, True)
            status_window.transient(self.master)
            
            # Create main frame with scrolling
            main_frame = ttk.Frame(status_window, padding="15")
            main_frame.pack(fill=tk.BOTH, expand=True)
            
            # Create scrolled text widget
            status_text = scrolledtext.ScrolledText(main_frame, wrap=tk.WORD, font=("Consolas", 10))
            status_text.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
            
            # Build status information
            status_content = "🧠 EMBEDDING MODEL STATUS REPORT\n"
            status_content += "=" * 45 + "\n\n"
            
            # Model information
            status_content += "🎨 MODEL INFORMATION:\n"
            status_content += "   • Model: all-MiniLM-L6-v2\n"
            status_content += "   • Provider: SentenceTransformers\n"
            status_content += "   • Purpose: Semantic similarity and uniqueness scoring\n\n"
            
            # Status for each component
            status_content += "🔧 COMPONENT STATUS:\n"
            status_content += f"   • Idea Generator: {idea_gen_status}\n"
            status_content += f"   • Content Planner: {planner_status}\n\n"
            
            # Check overall status
            embedding_working = ("Success" in idea_gen_status and "Success" in planner_status)
            
            if embedding_working:
                status_content += "✅ OVERALL STATUS: OPERATIONAL\n\n"
                status_content += "🔍 WHAT THIS ENABLES:\n"
                status_content += "   • Advanced semantic similarity calculations\n"
                status_content += "   • Accurate content uniqueness scoring\n"
                status_content += "   • Intelligent duplicate detection\n"
                status_content += "   • Better content recommendation\n\n"
                
                status_content += "📊 PERFORMANCE IMPACT:\n"
                status_content += "   • More accurate freshness scoring\n"
                status_content += "   • Better job selection algorithm\n"
                status_content += "   • Improved content diversity\n\n"
            else:
                status_content += "⚠️ OVERALL STATUS: LIMITED FUNCTIONALITY\n\n"
                status_content += "🔧 CURRENT LIMITATIONS:\n"
                status_content += "   • Using simple keyword-based similarity\n"
                status_content += "   • Less accurate uniqueness scoring\n"
                status_content += "   • Reduced duplicate detection capability\n\n"
                
                status_content += "🛠️ HOW TO FIX:\n"
                status_content += "   1. Install sentence-transformers:\n"
                status_content += "      pip install sentence-transformers\n\n"
                status_content += "   2. Restart the application\n\n"
                status_content += "   3. Check the Log tab for any error messages\n\n"
                
                status_content += "💡 FALLBACK BEHAVIOR:\n"
                status_content += "   • System uses keyword overlap for similarity\n"
                status_content += "   • Scoring still works but less accurately\n"
                status_content += "   • No loss of core functionality\n\n"
            
            # Technical details
            status_content += "🔍 TECHNICAL DETAILS:\n"
            status_content += "   • Vector dimensions: 384\n"
            status_content += "   • Context window: 256 tokens\n"
            status_content += "   • Similarity metric: Cosine similarity\n"
            status_content += "   • Storage: Binary serialized in database\n\n"
            
            # Insert content and make read-only
            status_text.insert('1.0', status_content)
            status_text.config(state=tk.DISABLED)
            
            # Add buttons
            button_frame = ttk.Frame(main_frame)
            button_frame.pack(pady=5)
            
            if not embedding_working:
                install_button = ttk.Button(button_frame, text="💲 Install sentence-transformers", 
                                           command=self.install_sentence_transformers)
                install_button.pack(side=tk.LEFT, padx=(0, 10))
            
            close_button = ttk.Button(button_frame, text="Close", command=status_window.destroy)
            close_button.pack(side=tk.LEFT)
            
        except Exception as e:
            self.log_message(f"Error showing embedding status: {e}", "error")
            messagebox.showerror("Status Error", f"Could not display embedding status: {e}")
    
    def install_sentence_transformers(self):
        """Helper function to install sentence-transformers."""
        import subprocess
        import sys
        
        try:
            self.log_message("💲 Installing sentence-transformers...", "info")
            result = subprocess.run([sys.executable, "-m", "pip", "install", "sentence-transformers"], 
                                  capture_output=True, text=True)
            
            if result.returncode == 0:
                self.log_message("✅ sentence-transformers installed successfully!", "success")
                messagebox.showinfo("Installation Complete", 
                                   "sentence-transformers has been installed successfully!\n\n"
                                   "Please restart the Content Strategist application to enable "
                                   "advanced embedding features.")
            else:
                self.log_message(f"❌ Installation failed: {result.stderr}", "error")
                messagebox.showerror("Installation Failed", 
                                    f"Failed to install sentence-transformers:\n\n{result.stderr}")
                
        except Exception as e:
            self.log_message(f"❌ Error during installation: {e}", "error")
            messagebox.showerror("Installation Error", f"Error during installation: {e}")

    def reinitialize_with_new_database(self, new_db_path: str):
        """
        Reinitialize the application with a new database.

        Args:
            new_db_path: Path to the new database file
        """
        self.log_message(f"🔄 Attempting to switch to database: {new_db_path}", "info")

        try:
            # 0. Ensure no critical operations are running (double check)
            if self.is_batch_running or self.is_running:
                self.log_message("❌ Cannot switch database while an operation is running.", "error")
                messagebox.showerror("Operation in Progress",
                                   "Cannot switch databases while an operation or batch is running. Please stop it first.")
                return

            # 1. Re-instantiate the main database object
            self.db = ContentDatabase(db_path=new_db_path)  # This will call init_database
            self.log_message(f"✅ MainWindow.db re-initialized with {new_db_path}", "info")

            # 2. Re-instantiate backend components with the new DB instance
            self.planner = ContentPlanner(db_instance=self.db, debug_mode=self.debug_mode)
            self.worker = ContentWorker(db_instance=self.db)
            self.idea_generator = IdeaGenerator(db_instance=self.db)
            self.log_message("✅ Backend components (Planner, Worker, IdeaGenerator) re-initialized with new DB.", "info")

            # 3. Update database instance in each tab and clear/refresh their specific data
            if hasattr(self, 'dashboard_tab'):
                self.dashboard_tab.update_database_instance(self.db, self.planner, self.worker)
                self.dashboard_tab.reset_for_new_database()
            if hasattr(self, 'content_plan_tab'):
                self.content_plan_tab.update_database_instance(self.db, self.planner)
                self.content_plan_tab.reset_for_new_database()
            if hasattr(self, 'idea_generator_tab'):
                self.idea_generator_tab.update_database_instance(self.db, self.idea_generator)
                self.idea_generator_tab.reset_for_new_database()
            self.log_message("✅ GUI Tabs DB references updated and states reset.", "info")

            # 4. Perform full UI refresh sequence
            self.populate_llm_dropdowns()  # Not DB dependent but good to refresh settings context

            self.log_message(f"🎉 Database switched successfully to {new_db_path}. UI refreshed.", "success")
            messagebox.showinfo("Database Switched", f"Successfully switched to database:\n{new_db_path}")

        except Exception as e:
            self.log_message(f"❌ Critical error during database switch: {e}", "error")
            import traceback
            self.log_message(traceback.format_exc(), "error")
            messagebox.showerror("Database Switch Failed",
                               f"Could not switch database: {e}\n\nApplication might be in an unstable state. Consider restarting.")

    def open_database_switcher_dialog(self):
        """Open the database switcher dialog."""
        try:
            DatabaseSwitcherDialog(self.master, self)
        except Exception as e:
            self.log_message(f"Error opening database switcher: {e}", "error")
            messagebox.showerror("Error", f"Failed to open database switcher:\n{str(e)}")

    def create_new_database_dialog(self, from_switcher: bool = False):
        """Create a new database dialog."""
        try:
            # Check if operations are running
            if self.is_batch_running or self.is_running:
                messagebox.showerror("Operation in Progress",
                                   "Cannot create new database while an operation is running. Please stop it first.")
                return

            # Get database name
            name = simpledialog.askstring("Database Name", "Enter a name for the new database:")
            if not name:
                return

            # Get database file path
            path = filedialog.asksaveasfilename(
                title="Choose Database File Location",
                defaultextension=".db",
                filetypes=[("SQLite Database", "*.db"), ("All Files", "*.*")]
            )
            if not path:
                return

            # Add database entry
            db_key = config_manager.add_database_entry(name, path)

            # Ask if user wants to activate this database now
            result = messagebox.askyesno("Activate Database",
                                       f"Database '{name}' created successfully.\n\n"
                                       "Do you want to activate this database now?")

            if result:
                config_manager.set_active_db_key(db_key)
                self.reinitialize_with_new_database(path)

            self.log_message(f"✅ New database created: {name} at {path}", "success")

        except Exception as e:
            self.log_message(f"Error creating new database: {e}", "error")
            messagebox.showerror("Error", f"Failed to create new database:\n{str(e)}")

    def toggle_selected_brand_focus(self):
        """Toggle the brand-centric status for selected content item(s) in the content plan."""
        try:
            selected_items = self.content_plan_tab.content_tree.selection()
            if not selected_items:
                messagebox.showwarning("No Selection", "Please select content item(s) to toggle brand focus.")
                return

            # Process each selected item
            updated_count = 0
            for item in selected_items:
                content_id = int(self.content_plan_tab.content_tree.item(item)['text'])

                # Get current brand-centric status
                content = self.db.get_content_by_id(content_id)
                if content:
                    current_status = content.get('is_brand_centric', False)
                    new_status = not current_status

                    # Update the database
                    success = self.db.update_brand_centric_status(content_id, new_status)
                    if success:
                        updated_count += 1

            if updated_count > 0:
                self.log_message(f"✅ Toggled brand focus for {updated_count} content item(s)", "success")

                # Refresh the content plan view
                self.content_plan_tab.refresh_content_plan()
            else:
                self.log_message("❌ Failed to toggle brand focus for any content items", "error")

        except Exception as e:
            self.log_message(f"Error toggling brand focus: {e}", "error")

    def set_selected_status_from_plan_tab(self, new_status):
        """Set the status for selected content item(s) in the content plan."""
        try:
            selected_items = self.content_plan_tab.content_tree.selection()
            if not selected_items:
                messagebox.showwarning("No Selection", "Please select content item(s) to set status.")
                return

            # Process each selected item
            updated_count = 0
            for item in selected_items:
                content_id = int(self.content_plan_tab.content_tree.item(item)['text'])

                # Update the database
                success = self.db.update_content_status(content_id, new_status)
                if success:
                    updated_count += 1

            if updated_count > 0:
                self.log_message(f"✅ Set status to '{new_status}' for {updated_count} content item(s)", "success")

                # Refresh displays
                self.update_next_job_info()
                self.content_plan_tab.refresh_content_plan()
                self.update_dashboard_status()
            else:
                self.log_message("❌ Failed to set status for any content items", "error")

        except Exception as e:
            self.log_message(f"Error setting status: {e}", "error")

    def on_closing(self):
        """Handle window closing."""
        if self.is_running:
            if messagebox.askokcancel("Job Running", "A job is currently running. Do you want to exit anyway?"):
                self.master.destroy()
        else:
            self.master.destroy()
