import shopify
import time

# --- Shopify API Session Management ---
# Avoid re-initializing the session for every call within a single run
_active_session = None

def _activate_shopify_session(shop_url: str, api_token: str, api_version: str = "2024-04"):
    """Activates the Shopify API session."""
    global _active_session
    # Ensure shop_url is just the domain name (remove https:// and trailing /)
    if shop_url.startswith("https://"):
        shop_url = shop_url[8:]
    if shop_url.endswith("/"):
        shop_url = shop_url[:-1]

    # Check if session is already active for this shop and token
    if shopify.ShopifyResource.site == f"https://{shop_url}/admin/api/{api_version}" and \
       shopify.ShopifyResource.password == api_token:
        print("Shopify session already active.")
        _active_session = shopify.Shop.current() # Re-confirm session validity
        return

    print(f"Activating Shopify session for {shop_url}...")
    session = shopify.Session(shop_url, api_version, api_token)
    shopify.ShopifyResource.activate_session(session)
    # Verify connection by fetching shop details
    _active_session = shopify.Shop.current()
    print(f"Shopify session activated for shop: {_active_session.name}")


def _deactivate_shopify_session():
    """Clears the active Shopify session."""
    global _active_session
    shopify.ShopifyResource.clear_session()
    _active_session = None
    print("Shopify session deactivated.")


def test_connection(shop_url: str, api_token: str, api_version: str = "2024-04") -> bool:
    """
    Tests the connection to the Shopify Admin API.

    Args:
        shop_url: The shop's myshopify.com URL (e.g., your-store.myshopify.com).
        api_token: The Admin API access token (password).
        api_version: The Shopify API version to use.

    Returns:
        True if the connection is successful.

    Raises:
        Exception: If the connection fails.
    """
    try:
        # Clean up shop URL if needed
        if shop_url.startswith("https://"):
            shop_url = shop_url[8:]
        if shop_url.endswith("/"):
            shop_url = shop_url[:-1]
            
        # Validate shop URL format
        if not (".myshopify.com" in shop_url or ".shopify.com" in shop_url):
            raise ValueError("Invalid shop URL format. It should be in the format: your-store.myshopify.com")

        # Create a session and try to connect
        session = shopify.Session(shop_url, api_version, api_token)
        shopify.ShopifyResource.activate_session(session)
        
        # Try to fetch shop info to validate connection
        shop = shopify.Shop.current()
        if not shop or not hasattr(shop, 'name'):
            raise ValueError("Could not retrieve shop information. Check your credentials.")
            
        print(f"Shopify connection test successful for shop: {shop.name}")
        return True
        
    except shopify.ShopifyResource.connection.ClientError as e:
        status_code = getattr(e.response, 'code', None)
        if status_code == 401:
            raise ValueError("Authentication failed. Invalid API token.")
        elif status_code == 404:
            raise ValueError("Shop not found. Check your shop URL.")
        else:
            raise ValueError(f"Shopify API error: {str(e)}")
            
    except ValueError as e:
        # Pass through our custom error messages
        print(f"Shopify connection test failed: {e}")
        raise
        
    except Exception as e:
        print(f"Shopify connection test failed: {e}")
        raise ValueError(f"Unexpected error connecting to Shopify: {str(e)}")
        
    finally:
        # Clean up the session after testing
        shopify.ShopifyResource.clear_session()


def post_blog_article(shop_url: str, api_token: str, title: str, content_html: str, author: str = "Automated Assistant", tags: str = "SEO, Automated", published: bool = False, blog_id: int = None, meta_description: str = None, api_version: str = "2024-04") -> shopify.Article:
    """
    Creates a new blog article on Shopify, optionally including a meta description.

    Args:
        shop_url: The shop's myshopify.com URL.
        api_token: The Admin API access token.
        title: The title of the blog article.
        content_html: The content of the article in HTML format.
        author: The author name (optional).
        tags: Comma-separated string of tags (optional).
        published: Set to True to publish immediately (default False).
        blog_id: The ID of the blog to post to. If None, tries to find the first blog.
        meta_description: The SEO meta description for the article (optional).
        api_version: The Shopify API version.

    Returns:
        The created shopify.Article object.

    Raises:
        Exception: If the post fails or the blog cannot be found.
    """
    try:
        _activate_shopify_session(shop_url, api_token, api_version)

        target_blog_id = blog_id
        if not target_blog_id:
            # Find the first available blog if no ID is specified
            blogs = shopify.Blog.find()
            if not blogs:
                raise Exception("No blogs found on the Shopify store.")
            target_blog_id = blogs[0].id
            print(f"No blog_id specified, using first found blog: ID={target_blog_id}, Title='{blogs[0].title}'")

        article = shopify.Article()
        article.blog_id = target_blog_id
        article.title = title
        article.author = author
        article.tags = tags
        article.body_html = content_html
        article.published = published

        # Add meta description to the summary field if provided
        if meta_description:
            print(f"Setting article summary (meta description): {meta_description}")
            article.summary = meta_description
            # Note: The summary field is Shopify's native field for meta descriptions/SEO

        if article.save():
            print(f"Successfully created Shopify article: ID={article.id}, Title='{article.title}'")
            # If metafields were added, they might not be immediately reflected in the returned object
            # depending on the library version. A separate fetch might be needed to confirm.
            return article
        else:
            # This part might be less common with pyactiveresource, errors usually raise exceptions
            errors = article.errors.full_messages() if hasattr(article.errors, 'full_messages') else 'Unknown error during save'
            raise Exception(f"Failed to save Shopify article: {errors}")

    except Exception as e:
        print(f"Error posting to Shopify: {e}")
        raise e # Re-raise
    finally:
        # Deactivate session, maybe? Or keep active if running multiple posts?
        # For single post per run, deactivating is cleaner.
         _deactivate_shopify_session()


def get_formatted_product_list(
    shop_url: str,
    api_token: str,
    api_version: str = "2024-04",
    max_products: int = 30, # Keep this reasonably low for prompt injection
    fields: str = "id,title,handle,product_type,tags,body_html" # ADD body_html
) -> str:
    """
    Fetches a list of products from Shopify and formats them into a string
    for LLM prompt injection, including a snippet of the product description.

    Args:
        shop_url: The shop's myshopify.com URL
        api_token: The Admin API access token
        api_version: The Shopify API version to use
        max_products: Maximum number of products to fetch
        fields: Comma-separated list of fields to fetch

    Returns:
        Formatted string of product information for LLM prompt injection
    """
    print(f"Fetching up to {max_products} products from Shopify...")
    product_list_for_prompt = []
    try:
        _activate_shopify_session(shop_url, api_token, api_version)

        # Fetch products
        products = shopify.Product.find(limit=max_products, fields=fields)

        if products:
            print(f"Fetched {len(products)} products.")

            # Construct base_product_url more robustly
            # Extract the domain from shop_url and build the correct product URL base
            if shop_url.startswith("https://"):
                domain = shop_url[8:].split('/')[0]  # Remove https:// and get domain only
            elif shop_url.startswith("http://"):
                domain = shop_url[7:].split('/')[0]  # Remove http:// and get domain only
            else:
                domain = shop_url.split('/')[0]  # Assume it's just the domain

            base_product_url = f"https://{domain}/products/"

            for product in products:
                # Use defensive getattr calls with proper fallbacks
                title = getattr(product, 'title', 'N/A')
                handle_val = getattr(product, 'handle', None)
                product_type = getattr(product, 'product_type', 'N/A')
                tags_list_attr = getattr(product, 'tags', None)  # getattr can return None for tags
                body_html_val = getattr(product, 'body_html', None)

                # Ensure title is treated as a string
                title_str = str(title) if title is not None else "N/A"
                product_type_str = str(product_type) if product_type is not None else "N/A"

                # Ensure tags_list is treated as a string, even if it's None or empty
                tags_display_str = str(tags_list_attr) if tags_list_attr else "None"

                description_snippet = "N/A"
                if body_html_val is not None and isinstance(body_html_val, str):
                    import re
                    clean_description = re.sub(r'<[^>]+>', ' ', body_html_val)
                    clean_description = ' '.join(clean_description.split())
                    description_snippet = clean_description[:150] + "..." if len(clean_description) > 150 else clean_description
                elif body_html_val is not None:  # If not string but not None (e.g. some other object)
                    description_snippet = str(body_html_val)[:150] + "..." if len(str(body_html_val)) > 150 else str(body_html_val)

                if handle_val and isinstance(handle_val, str) and handle_val.strip():
                    url = f"{base_product_url}{handle_val}"
                    product_list_for_prompt.append(
                        f"- Name: {title_str}\n  Type: {product_type_str}\n  URL: {url}\n  Description Snippet: {description_snippet}\n  Tags: {tags_display_str}"
                    )
                else:
                    product_id_for_log = getattr(product, 'id', 'UnknownID')
                    print(f"⚠️ Product '{title_str}' (ID: {product_id_for_log}) skipped due to missing or invalid handle: {repr(handle_val)}")
                    continue
        else:
            print("No products found.")

    except Exception as e:
        print(f"Error fetching Shopify products: {e}")
        return f"Error fetching product list: {e}"
    finally:
        _deactivate_shopify_session()

    return "\n".join(product_list_for_prompt)


def fetch_recent_posts(shop_url: str, api_token: str, limit: int = 50, api_version: str = "2024-04") -> list[dict]:
    """
    Fetches recent blog articles from Shopify.

    Args:
        shop_url: The shop's myshopify.com URL.
        api_token: The Admin API access token.
        limit: The maximum number of articles to fetch.
        api_version: The Shopify API version.

    Returns:
        A list of dictionaries, each representing an article with keys like
        'id', 'title', 'summary_html', 'body_html', 'published_at'.
        Returns an empty list on error.
    """
    print(f"Fetching up to {limit} recent posts from Shopify...")
    articles_data = []
    try:
        _activate_shopify_session(shop_url, api_token, api_version)

        # Fetch articles, ordered by published_at descending (newest first)
        # Note: The 'order' parameter might depend on the specific library version's capabilities.
        # If 'order' doesn't work directly, we might need to fetch more and sort manually,
        # but let's try the direct approach first. The REST API supports it.
        # We also fetch specific fields to reduce data transfer.
        recent_articles = shopify.Article.find(limit=limit, order="published_at DESC", fields="id,title,summary_html,body_html,published_at")

        if recent_articles:
            print(f"Fetched {len(recent_articles)} articles.")
            for article in recent_articles:
                articles_data.append({
                    "id": article.id,
                    "title": getattr(article, 'title', 'No Title'),
                    "summary_html": getattr(article, 'summary_html', ''),
                    "body_html": getattr(article, 'body_html', ''),
                    "published_at": getattr(article, 'published_at', None)
                })
        else:
            print("No recent articles found.")

    except Exception as e:
        print(f"Error fetching recent Shopify posts: {e}")
        # Return empty list on error
        return []
    finally:
        _deactivate_shopify_session()

    return articles_data


# Example usage (optional, for testing)
if __name__ == '__main__':
    import os
    from dotenv import load_dotenv
    # Load API key from a .env file in the parent directory for testing
    env_path = os.path.join(os.path.dirname(__file__), '..', '.env')
    load_dotenv(dotenv_path=env_path)

    test_shop_url = os.getenv('SHOPIFY_STORE_URL')
    test_api_token = os.getenv('SHOPIFY_API_TOKEN')

    if test_shop_url and 'your-store' not in test_shop_url and test_api_token and 'YOUR_' not in test_api_token:
        print("--- Testing Shopify Connection ---")
        try:
            if test_connection(test_shop_url, test_api_token):
                print("\n--- Testing Blog Post Creation (Draft) ---")
                try:
                    # Create a dummy post
                    timestamp = time.strftime("%Y-%m-%d %H:%M:%S")
                    new_article = post_blog_article(
                        shop_url=test_shop_url,
                        api_token=test_api_token,
                        title=f"Test Post from SEO Assistant ({timestamp})",
                        content_html="<p>This is a test blog post created automatically.</p>",
                        tags="Test, Automated",
                        published=False # Keep as draft
                    )
                    print(f"Draft article created successfully! ID: {new_article.id}")
                    # You might want to manually delete this test post from your Shopify admin
                except Exception as post_e:
                    print(f"Blog post creation failed: {post_e}")
            else:
                 print("Connection Test returned False (unexpected).") # Should not happen
        except Exception as conn_e:
            print(f"Connection test failed: {conn_e}")
    else:
        print("SHOPIFY_STORE_URL or SHOPIFY_API_TOKEN not found in .env or are placeholders.")
        print(f"Ensure {env_path} exists and contains:")
        print("SHOPIFY_STORE_URL=your-store.myshopify.com")
        print("SHOPIFY_API_TOKEN=your_admin_api_access_token")
