"""
GUI Utility Functions

This module contains utility functions used across the GUI components
for data processing, formatting, and display operations.
"""

import struct
from typing import Dict, Any


def decode_freshness_score(score, item_name="Unknown"):
    """
    Utility function to safely decode freshness scores that might be stored as bytes.
    
    Args:
        score: The score value (could be bytes, float, string, or None)
        item_name: Name of the item for debugging (optional)
    
    Returns:
        float: Properly decoded score value
    """
    if score is None:
        return 0.0
    
    if isinstance(score, bytes):
        try:
            if len(score) == 4:
                # Decode 32-bit IEEE 754 float
                decoded = struct.unpack('f', score)[0]
                print(f"✅ DECODE [{item_name}]: Decoded {repr(score)} to {decoded:.2f}")
                return float(decoded)
            elif len(score) == 8:
                # Decode 64-bit IEEE 754 double
                decoded = struct.unpack('d', score)[0]
                print(f"✅ DECODE [{item_name}]: Decoded 64-bit {repr(score)} to {decoded:.2f}")
                return float(decoded)
            else:
                print(f"⚠️ DECODE [{item_name}]: Invalid bytes length {len(score)}, using 0.0")
                return 0.0
        except (struct.error, ValueError) as e:
            print(f"❌ DECODE [{item_name}]: Error decoding bytes {repr(score)}: {e}")
            return 0.0
    
    # Handle string or numeric values
    try:
        return float(score)
    except (ValueError, TypeError):
        print(f"⚠️ DECODE [{item_name}]: Could not convert {repr(score)} to float, using 0.0")
        return 0.0


def safe_sort_key_for_freshness(item_dict: Dict[str, Any]) -> float:
    """
    Safe sort key function for freshness scores that handles bytes properly.
    
    Args:
        item_dict: Dictionary containing content data
    
    Returns:
        float: Properly decoded score for sorting
    """
    score = item_dict.get('freshness_score', 0.0)
    keyword = item_dict.get('keyword', 'Unknown')
    return decode_freshness_score(score, keyword)
