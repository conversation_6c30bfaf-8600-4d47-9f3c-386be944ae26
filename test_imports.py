"""
Quick import test for batch automation components
"""

import sys
import os

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(__file__))

def test_imports():
    """Test if all required modules can be imported."""
    
    print("Testing imports for batch automation...")
    
    try:
        from database import ContentDatabase
        print("✅ database.py imported successfully")
    except Exception as e:
        print(f"❌ Failed to import database: {e}")
        return False
    
    try:
        from planner import ContentPlanner
        print("✅ planner.py imported successfully")
    except Exception as e:
        print(f"❌ Failed to import planner: {e}")
        return False
    
    try:
        from worker import ContentWorker
        print("✅ worker.py imported successfully")
    except Exception as e:
        print(f"❌ Failed to import worker: {e}")
        return False
    
    try:
        from core import config_manager
        print("✅ config_manager imported successfully")
    except Exception as e:
        print(f"❌ Failed to import config_manager: {e}")
        return False
    
    try:
        from gui.dashboard_tab import DashboardTab
        print("✅ dashboard_tab.py imported successfully")
    except Exception as e:
        print(f"❌ Failed to import dashboard_tab: {e}")
        return False
    
    # Test basic database functionality
    try:
        db = ContentDatabase()
        print("✅ Database connection established")
    except Exception as e:
        print(f"❌ Database connection failed: {e}")
        return False
    
    # Test planner initialization
    try:
        planner = ContentPlanner(debug_mode=False)  # Disable debug for clean output
        print("✅ Planner initialized")
    except Exception as e:
        print(f"❌ Planner initialization failed: {e}")
        return False
    
    print("\n🎉 All imports successful! Batch automation feature is ready to use.")
    return True

if __name__ == "__main__":
    success = test_imports()
    if success:
        print("\nYou can now run the main SEO Assistant GUI with the new batch automation feature.")
        exit(0)
    else:
        print("\nSome imports failed. Please check the error messages above.")
        exit(1)
