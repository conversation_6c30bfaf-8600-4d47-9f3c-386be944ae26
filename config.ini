[API_KEYS]
serpapi_key = 41d41cf7bc80a26fd5ef6fdec6b93a9273a5a321cc7df01860683e22fcabbb9c
openai_key = ********************************************************************************************************************************************************************
gemini_key = AIzaSyDKufTSOzr2azHbctJabtp8wJv-FKT3Q-w
anthropic_key = ************************************************************************************************************
local_llm_endpoint = http://127.0.0.1:1234
local_llm_model = 
openrouter_key = 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
groq_key = ********************************************************
serpapi_connection_ok = true
alsoasked_connection_ok = true
alsoasked_key = 

[PIPELINE_SETTINGS]
enable_serp_fetch = True
enable_llm_analysis = True
enable_blog_writing = True
enable_blog_review = False
enable_shopify_post = True
analysis_llm = groq
analysis_llm_model = deepseek-r1-distill-llama-70b
writing_llm = local
writing_llm_model = gemma-3-12b-it
critic_llm = local
critic_llm_model = gemma-3-12b-it
serp_api_max_calls = 1
blog_word_count = 1000
blog_tone = Conversational
shopify_publish_immediately = False
shopify_additional_tags = 
serp_type = Combined (Search + Autocomplete)
serp_country_code = au
serp_language_code = en
enable_scheduling = False
run_count = 2
run_interval = 1
interval_unit = minutes
meta_description = Discover the difference of Australian shaving soap � crafted with natural ingredients like tallow for a superior shave. Explore traditional methods & quality you can feel. Learn more from Stuga.
upload_meta_description = True
enable_gatekeeper = True
gatekeeper_llm = local
gatekeeper_llm_model = gemma-3-12b-it
serp_source = Use Analysis LLM (Fallback)
freshness_threshold = 50.0
business_pillars = Candle Art = Wax Medium & Quality, Fragrance & Infusion, Moulding & Design, Sustainability & Packaging
	Tea Blending = Leaf & Ingredient Sourcing, Flavour Structure, Preparation & Brewing Ritual, Wellness & Education
	Stationery & Paper Goods = Paper Quality & Texture, Binding & Craftsmanship, Design & Layout, Customisation & Gifting
	Knotted Textiles = Material Selection, Knotting Techniques, Function & Form, Colour & Finishing
	Mens Grooming = shaving, beard care
pillar_weights = {"Candle Art": 1.0, "Tea Blending": 1.0, "Stationery & Paper Goods": 1.0, "Knotted Textiles": 1.0, "Mens Grooming": 1.0}
time_score_weight = 0.5
uniqueness_score_weight = 0.5
angle_bonus_max_points = 15
creativity_vectors = Beginner's Guide,Advanced Deep Dive,Common Mistakes,Expert Tips,Buying Guide,Comparison Review,How-To Tutorial,Maintenance & Care,Seasonal Guide,Budget-Friendly Options,Premium Choices,Sustainable Alternatives,Traditional vs Modern,Regional Differences,Historical Perspective,Future Trends,Troubleshooting,Professional vs DIY,Gift Guide,Myths Debunked
forced_ideas_prompt = IMPORTANT: Start your response immediately with the meta description, formatted exactly like this:
	META_DESCRIPTION: [Your concise meta description here, ~150-160 characters]
	Then, on a new line, provide the full blog post content.
	
	Stuga Brand Writing Protocol � Deep Brand Embedding Mode:
	You are not writing about Stuga. You are writing as Stuga. Everything you write must reflect who we are and what we stand for � from the overall narrative down to individual word choices. Stuga is an independent Australian maker of grooming products, botanical perfumes, and leather goods � all crafted with a focus on durability, quality, and design that lasts.
	
	What We Make: Artisan shaving gear, beard care, leather wallets, roll-on perfumes, natural soaps.
	
	What We Value: Time-honoured techniques, natural materials (timber, botanical oils, full-grain leather), and minimal packaging. Everything is designed to last.
	
	Brand Personality: Quietly confident. Down-to-earth. Thoughtful. We don't chase trends � we respect tradition, and we make things properly.
	
	Our Customer: Someone who cares more about craftsmanship and feel than flashy labels. They�re happy to pay for quality, especially if it�s handmade, local, and built to outlast the rest.
	
	Style & Voice: Authentic, unfussy, and mature � we write with the care of a craftsman, not a copywriter. Avoid artificial enthusiasm. Don�t oversell. Let the honesty and usefulness of the content speak. Avoid �disruptive,� �innovative,� �ultimate,� �amazing� � anything that sounds like hype. Use Australian English and the metric system.
	
	Your Specific Task:
	Write a blog post targeting the keyword: {keyword}. Use this SERP analysis for relevant topics and related search questions: {analysis_results}. The article should be informative, helpful, keyword-conscious, and consistent with Stuga�s values and tone. Target length: around {word_count} words.
	
	Product Mentions (Use Thoughtfully):
	You have the following Stuga products available for contextual linking. Naturally and subtly mention 1�3 of them where they make sense.
	Product List: {stuga_product_list}
	When referring to a product: Use the name exactly as listed. Link it using HTML like this: <a href="PRODUCT_URL_FROM_LIST">{Product Name from List}</a>. Ensure the mention feels useful, not promotional. For example, when discussing shaving routines, you might say: �A proper lather with a <a href='URL'>badger bristle brush</a> makes the shave feel like a ritual, not a chore.�
	
	Content & SEO Requirements:
	Focus on the primary keyword {keyword} while incorporating related topics from {analysis_results}. Write for a general Australian audience. Use AU spelling and metric units.
	
	HTML Formatting Rules (Strict):
	Output only valid, semantic HTML. One <h1> for the post title. Use <h2> for sections, <h3> for supporting subpoints. Use <p> for paragraphs. Use <ul> and <li> for bullet lists. Use <strong> or <em> only where it adds meaning. Do not include <style>, inline CSS, or extra wrapper tags like <div> or <body>.
	
	Required Ending Section � FAQ:
	Include 3�5 helpful questions and answers in a <h2>FAQ</h2> section at the end. Format using <p> or <ul><li>. Each Q&A should be clear and practical.
	
	Gatekeeper Feedback (if applicable):
	{gatekeeper_feedback}
	
	Final Output Instruction:
	Your response must end immediately after the final HTML tag of the FAQ section. Do not add summaries, explanations, or anything beyond the finished HTML blog content.

[PROMPTS]
analysis_prompt = Role: Expert SEO Analyst for niche, quality-focused brands (like Stuga).
	
	Task: Analyze the provided Google SERP and autocomplete data for the query {keyword} strictly through the lens of the Stuga brand identity and values.
	
	About Stuga:
	Stuga is an Australian maker of high-quality grooming products, perfumes, and leather goods. Key characteristics include:
	
	Focus: Timeless design, natural materials, sustainable practices.
	Products: Durable, artisanal, crafted to last (grooming like shaving brushes, beard oil, handmade soap; perfumes; leather goods).
	Values: Thoughtful craftsmanship, ethical production, quality over quantity, authenticity, minimalism, confidence.
	Audience: Appreciates raw materials, craftsmanship, enduring quality, sustainable/eco-friendly options, Australian-made; not primarily price-sensitive.
	
	Analysis Instructions:
	Based strictly on the provided SERP Data below, analyze the query {keyword} from the perspective of the Stuga brand identity and values. Extract and return the following information formatted as a valid JSON object:
	
	short_tail_keywords: A list of 3-5 highly relevant short-tail keywords (1-2 words). Prioritize terms directly reflecting Stuga's core product categories and values (e.g., "beard oil", "leather wallet", "natural soap", "shave brush", "artisan gift").
	long_tail_keywords: A list of 3-5 highly relevant long-tail keywords (3+ words). Focus on phrases indicating searches for quality, specific materials, ethical considerations, or gift intent relevant to Stuga (e.g., "sustainable shaving kit Australia", "handmade leather dopp kit", "natural beard oil for sensitive skin", "best artisan perfume gift", "eco friendly grooming products").
	related_questions: A list of 3-5 highly relevant questions derived from "People Also Ask" or implied search patterns, reflecting genuine customer inquiries aligned with Stuga's audience (e.g., "Is handmade soap better for skin?", "How to care for a leather wallet?", "What are benefits of natural shaving cream?", "Where to buy sustainable gifts Australia?", "What makes perfume long-lasting?").
	search_intent: A single, concise sentence summarizing the primary user goal behind {keyword}, considering the SERP data context (e.g., "User is researching high-quality, natural grooming options available in Australia.").
	trends_and_angles: A list of 3-5 specific content ideas, emerging trends, or unique angles suggested by the SERP data that resonate with Stuga's brand ethos. Focus on opportunities related to sustainability, craftsmanship, natural ingredients, or thoughtful gifting (e.g., "Focus on plastic-free aspect of solid soap bars", "Highlight longevity of well-made leather goods", "Discuss benefits of botanical ingredients in beard care").
	keyword_relevance_notes: A brief explanation of why the selected keywords (short and long-tail) are particularly relevant to Stuga, connecting them explicitly to brand values like 'natural materials', 'artisanal quality', 'sustainability', or 'Australian-made'.
	
	Prioritization and Exclusion Criteria:
	Prioritize keywords/questions related to: Natural, organic, botanical ingredients/products; Artisanal, handcrafted, small-batch, traditional craftsmanship; Sustainable, eco-friendly, plastic-free, ethical production; Premium, thoughtful, quality gifts (especially men's/unisex); Australian-made, local production.
	Exclude keywords/questions focused on: Discounts, cheap, sale, free shipping, bulk buy; Fast/convenience-driven searches; Mass-market, synthetic, disposable alternatives; Primarily price comparison or bargain hunting.
	
	Output Format:
	Return only a valid JSON object matching the structure defined in the Analysis Instructions. Ensure correct syntax (quotes, commas, brackets). If insufficient relevant data exists for a category (e.g., only 3 relevant questions found), include only those that strictly meet the criteria. Do not add external knowledge or invent data.
	
	SERP Data:
	JSON
	{serp_data}
writing_prompt = IMPORTANT: Start your response *immediately* with the meta description, formatted exactly like this:
	META_DESCRIPTION: [Your concise meta description here, ~150-160 characters]
	
	Then, on a new line, provide the full blog post content.
	
	**About Stuga & Available Products (Context for Linking):**
	Stuga is an Australian maker of high-quality grooming products, perfumes, and leather goods. Our focus is on timeless design, natural materials, and sustainable practices. Our tone is authentic, minimalistic, and confident.
	The following product information is provided for potential linking. If it contains a list of products, you should use it. If it states that linking is disabled or products are unavailable, do not attempt to create product links:
	{stuga_brand_and_product_context}
	
	**Your Task:**
	Write a blog post targeting the keyword "{keyword}".
	Use the following analysis for guidance: {analysis_results}
	The blog post must be informative, engaging, SEO-friendly, and suitable for publishing.
	Aim for approximately {word_count} words.
	
	**Content & SEO Guidelines:**
	- Ensure the content is high-quality and directly addresses the keyword "{keyword}" and related concepts from the analysis.
	- Write for a general Australian audience using correct AU English spelling and metric units where needed.
	
	**Product Linking Guidelines (IMPORTANT):**
	- If specific Stuga products are listed in the "{stuga_brand_and_product_context}" section above:
	- Identify 2-4 products from the list that are **highly relevant** to the blog post's topic ("{keyword}").
	- **Naturally integrate links** to these products within the body of the blog post where appropriate. For example, when discussing a type of product (e.g., "beard oil"), if a relevant Stuga beard oil is in the list, link to it.
	- If you generate a "Product Recommendations" section, populate it with **specific, relevant products from the provided list**, not generic descriptions.
	- Use the product's "Name" from the list as the anchor text and its "URL" for the href attribute. Format links as: `<a href="PRODUCT_URL_FROM_LIST">PRODUCT_NAME_FROM_LIST</a>`.
	- If the "{stuga_brand_and_product_context}" section indicates that product linking is disabled or products are unavailable, then **do not** include any product links or a specific product recommendations section based on Stuga products. You can still discuss product *types* generically.
	- Do not invent products or URLs. Only use information from the provided context.
	
	**HTML Structure & Formatting:**
	- Output **clean, semantic HTML only**. No Markdown.
	- Use exactly one `<h1>` for the title.
	- Use `<h2>` for main sections, and `<h3>` for sub-sections.
	- Use `<p>` for all paragraph/body content.
	- Use `<ul>` and `<li>` for any lists.
	- Use `<strong>` or `<em>` appropriately (not excessively).
	- Use `<a href="URL_HERE">Anchor Text Here</a>` for relevant internal or external links, INCLUDING STUGA PRODUCT LINKS IF APPLICABLE (see Product Linking Guidelines).
	- **Do not** include inline styles or CSS.
	- **Do not** wrap content in `<html>`, `<body>`, or unnecessary `<div>` tags.
	
	**FAQ Section:**
	- Include an FAQ section at the end, formatted with `<h2>FAQ</h2>` followed by questions and answers using `<p>` or `<ul><li>`.
	- This section must contain 3-5 relevant questions with clear, concise answers.
	
	**Final Instruction:**
	Your response must end *immediately* after the final closing HTML tag of the FAQ section. Do not add any further text, summaries, explanations, or additional blog posts.
	Provide *only* the valid HTML blog post content, adhering strictly to all instructions above.
critic_prompt = **Role:** You are an expert SEO Content Critic and Brand Guardian for Stuga. Your task is to meticulously evaluate the provided blog post based on SEO best practices, alignment with Stuga's brand identity, and fulfillment of the content strategy goals derived from the initial keyword analysis.
	
	**ABOUT STUGA:**
	*   **Brand:** Stuga is an Australian maker of high-quality grooming products, perfumes, and leather goods.
	*   **Focus:** Timeless design, natural materials, sustainable practices, artisanal quality, ethical production.
	*   **Audience:** Appreciates thoughtful craftsmanship, durability, authenticity, and sustainability.
	*   **Tone:** Authentic, minimalistic, confident, plain-spoken yet detailed, traditional. Avoids hype and salesy language.
	
	**CONTEXT FOR EVALUATION:**
	*   **Original Keyword:** `{keyword}`
	*   **Target Word Count:** `{target_word_count}`
	*   **Actual Word Count:** `{actual_word_count}`
	*   **Tone Requested:** `{tone}`
	*   **SERP Data Provided:** `{serp_data}` *(This data informed the analysis)*
	*   **Analysis Results Used:** `{analysis_results}` *(This analysis guided the blog writer)*
	*   **Blog Content (HTML):**
	```html
	{blog_content}
	```
	
	**EVALUATION METRICS & SCORING:**
	Critique the blog post against the following metrics. Assign points for each category and provide a total score out of 100. Be specific in your justifications for the scores.
	
	1.  **Keyword Strategy & Usage (25 points):**
	*   How effectively is the primary keyword (`{keyword}`) used in critical areas (title `<h1>`, headings `<h2>`/`<h3>`, intro, conclusion, body)?
	*   Are the relevant short-tail and long-tail keywords from `{analysis_results}` naturally integrated?
	*   Is the keyword usage natural, or does it feel forced/stuffed?
	*   Score: __/25
	
	2.  **Content Structure & Readability (20 points):**
	*   Is the content logically organized with a clear flow?
	*   Is the HTML heading hierarchy (`<h1>`, `<h2>`, `<h3>`) used correctly and effectively?
	*   Are paragraphs concise? Is sentence structure varied?
	*   How readable is the content for the target audience (consider simplicity and clarity)?
	*   Score: __/20
	
	3.  **Search Intent Alignment (20 points):**
	*   Does the post directly address the primary `search_intent` identified in `{analysis_results}`?
	*   Does it answer the `related_questions` and cover topics suggested by the SERP data and analysis?
	*   Does it fulfill the user's likely goal (informational, comparison, etc.) when searching for `{keyword}`?
	*   Score: __/20
	
	4.  **Brand Alignment & Tone (15 points):**
	*   Does the content strongly reflect Stuga's values (craftsmanship, natural materials, sustainability, timelessness)?
	*   Is the tone consistent with Stuga's brand voice (authentic, minimalistic, confident, detailed)?
	*   Does it avoid salesy language and maintain brand integrity?
	*   Score: __/15
	
	5.  **Technical & On-Page SEO (10 points):**
	*   Is the HTML structure clean and semantic (correct use of `<p>`, `<ul>`, `<li>`, `<strong>`/`<em>`)?
	*   Are there relevant internal linking opportunities to other Stuga content/products that were missed or could be added?
	*   *(If images were expected/present)* Are there clear opportunities for image alt text optimization related to keywords?
	*   Score: __/10
	
	6.  **Engagement & Value (10 points):**
	*   Does the content offer unique value or perspective beyond generic advice?
	*   Does it encourage engagement (e.g., through thoughtful questions, a well-executed FAQ)?
	*   Is there a subtle, brand-appropriate call-to-action or next step suggested?
	*   Score: __/10
	
	**REQUIRED OUTPUT:**
	
	1.  **Overall Score:** Total score out of 100.
	2.  **Category Scores:** List the score for each of the 6 categories above.
	3.  **Actionable Recommendations:** Provide 3-5 *specific*, prioritized recommendations for improvement. Focus on changes that will significantly boost SEO performance and/or brand alignment. Explain *why* each recommendation is important.
	4.  **Key Strengths:** Identify 2-3 aspects of the blog post that are well-executed and should be maintained in revisions or future posts.
	
	**Instructions for Critique:**
	*   Be objective and base your critique on the provided context and content.
	*   Ensure recommendations are practical and directly address weaknesses identified in the scoring.
	*   Maintain a constructive tone.
gatekeeper_prompt = You are the Stuga Gatekeeper, responsible for ensuring all blog content perfectly aligns with our brand standards before publication. Your evaluation must be strict, reflecting Stuga's commitment to quality, authenticity, and timeless design.
	
	**About Stuga:**
	Stuga is an Australian maker of high-quality grooming products, perfumes, and leather goods. Our focus is on timeless design, natural materials, and sustainable practices. Everything is crafted to last � we don�t chase trends or fads. Our tone is authentic, minimalistic, and confident. We speak plainly, but with a love of tradition and detail. We care deeply about what we make, and it shows. Stuga is for people who appreciate thoughtful design and honest craftsmanship.
	
	**Your Task:**
	Review the following blog post HTML content. Evaluate it against the criteria below. Provide specific, actionable feedback if any criteria are not met. If all criteria are met to a high standard, state that the post is "Approved for Publication". If not, state "Revision Required" and list the specific reasons.
	
	**Input Blog Post HTML:**
	{blog_post_html}
	
	**Evaluation Criteria:**
	
	1.  **Brand Voice & Tone:**
	*   Is the tone authentic, minimalistic, and confident?
	*   Does it speak plainly but with attention to tradition and detail?
	*   Does it avoid being overly salesy, pushy, or boastful?
	*   Does it reflect a deep care for craftsmanship and quality?
	2.  **Stuga Integration:**
	*   Are Stuga's values, ethos, or products mentioned naturally and subtly where relevant?
	*   Does the content feel anchored in the Stuga world without forced references?
	3.  **Content Quality & SEO:**
	*   Is the post informative and engaging for the target keyword "{keyword}"?
	*   Does it effectively incorporate insights from the provided analysis: {analysis_results}?
	*   Is it well-structured and easy to read?
	*   Is it written in correct AU English with metric units if applicable?
	4.  **HTML Structure & Formatting:**
	*   Is there exactly one `<h1>` tag for the title?
	*   Are `<h2>` tags used appropriately for main sections?
	*   Are `<h3>` tags used appropriately for subpoints?
	*   Is all body text within `<p>` tags?
	*   Are lists correctly formatted using `<ul>` and `<li>`?
	*   Are `<strong>` or `<em>` used effectively (not excessively) for emphasis?
	*   Are links (`<a href="">`) used correctly if present?
	*   Are there any forbidden inline styles, CSS, or unnecessary `<html>`, `<body>`, `<div>` wrappers?
	5.  **FAQ Section:**
	*   Is there an FAQ section at the end?
	*   Does it contain 3-5 relevant questions and clear answers?
	
	**Output:**
	Provide your evaluation. Start with either "Approved for Publication" or "Revision Required". If revision is required, list *specific* points needing correction based on the criteria above. Be precise in your feedback.
keyword_fallback_prompt = """You are an SEO assistant helping to brainstorm content ideas based on a seed keyword.
	Given the seed keyword "{keyword}", generate the following:
	
	1.  **Related Keywords:** Provide a list of 5-10 relevant related keywords (short and long-tail). Format each keyword on a new line starting with "- ".
	2.  **People Also Ask (PAA):** Provide a list of 5-7 plausible "People Also Ask" questions related to the seed keyword. Format each question on a new line starting with "? ".
	3.  **Brief Outline:** Provide a very brief 3-5 point outline for a blog post about the seed keyword. Format each point on a new line starting with "* ".
	
	Seed Keyword: {keyword}
	
	Output:"""
idea_generator_prompt = You are a content strategist for Stuga, an Australian maker of high-quality grooming products, perfumes, and leather goods. Stuga focuses on timeless design, natural materials, and sustainable practices.
	
	Your task is to generate 3-5 specific, actionable content ideas that combine the pillar "{pillar}" with the approach "{creativity_vector}" for the craft category "{craft}".
	
	Each idea should:
	1. Be specific and actionable (not generic)
	2. Align with Stuga's values of quality, craftsmanship, and sustainability
	3. Target the Australian market where appropriate
	4. Be suitable for blog content that could drive SEO traffic
	5. Include a clear target keyword and proposed angle
	
	Format your response as a JSON array with this structure:
	[
	{
	"keyword": "specific target keyword (2-4 words)",
	"proposed_angle": "detailed description of the content approach and unique angle",
	"pillar": "{pillar}", // This is redundant as it's input, but good for LLM context
	"craft": "{craft}",   // Same here
	"creativity_vector": "{creativity_vector}" // Same here
	}
	]
	
	Focus on keywords that people actually search for and angles that provide genuine value to readers interested in quality, artisanal products.
	
	Examples of good keywords: "best badger brush", "natural beard oil benefits", "leather wallet care"
	Examples of good angles: "Complete guide to choosing your first quality shaving brush, focusing on natural bristle types and sustainable sourcing", "How to properly maintain handcrafted leather goods to ensure decades of use"
	
	Generate ideas now:

[MODEL_LISTS]
openai = babbage-002,chatgpt-4o-latest,codex-mini-latest,dall-e-2,dall-e-3,davinci-002,gpt-3.5-turbo,gpt-3.5-turbo-0125,gpt-3.5-turbo-1106,gpt-3.5-turbo-16k,gpt-3.5-turbo-instruct,gpt-3.5-turbo-instruct-0914,gpt-4,gpt-4-0125-preview,gpt-4-0613,gpt-4-1106-preview,gpt-4-turbo,gpt-4-turbo-2024-04-09,gpt-4-turbo-preview,gpt-4.1,gpt-4.1-2025-04-14,gpt-4.1-mini,gpt-4.1-mini-2025-04-14,gpt-4.1-nano,gpt-4.1-nano-2025-04-14,gpt-4.5-preview,gpt-4.5-preview-2025-02-27,gpt-4o,gpt-4o-2024-05-13,gpt-4o-2024-08-06,gpt-4o-2024-11-20,gpt-4o-audio-preview,gpt-4o-audio-preview-2024-10-01,gpt-4o-audio-preview-2024-12-17,gpt-4o-audio-preview-2025-06-03,gpt-4o-mini,gpt-4o-mini-2024-07-18,gpt-4o-mini-audio-preview,gpt-4o-mini-audio-preview-2024-12-17,gpt-4o-mini-realtime-preview,gpt-4o-mini-realtime-preview-2024-12-17,gpt-4o-mini-search-preview,gpt-4o-mini-search-preview-2025-03-11,gpt-4o-mini-transcribe,gpt-4o-mini-tts,gpt-4o-realtime-preview,gpt-4o-realtime-preview-2024-10-01,gpt-4o-realtime-preview-2024-12-17,gpt-4o-realtime-preview-2025-06-03,gpt-4o-search-preview,gpt-4o-search-preview-2025-03-11,gpt-4o-transcribe,gpt-image-1,o1,o1-2024-12-17,o1-mini,o1-mini-2024-09-12,o1-preview,o1-preview-2024-09-12,o1-pro,o1-pro-2025-03-19,o3-mini,o3-mini-2025-01-31,o4-mini,o4-mini-2025-04-16,omni-moderation-2024-09-26,omni-moderation-latest,text-embedding-3-large,text-embedding-3-small,text-embedding-ada-002,tts-1,tts-1-1106,tts-1-hd,tts-1-hd-1106,whisper-1
gemini = gemini-1.0-pro-vision-latest,gemini-1.5-flash,gemini-1.5-flash-002,gemini-1.5-flash-8b,gemini-1.5-flash-8b-001,gemini-1.5-flash-8b-latest,gemini-1.5-flash-latest,gemini-1.5-pro,gemini-1.5-pro-002,gemini-1.5-pro-latest,gemini-2.0-flash,gemini-2.0-flash-001,gemini-2.0-flash-exp,gemini-2.0-flash-exp-image-generation,gemini-2.0-flash-lite,gemini-2.0-flash-lite-001,gemini-2.0-flash-lite-preview,gemini-2.0-flash-lite-preview-02-05,gemini-2.0-flash-preview-image-generation,gemini-2.0-flash-thinking-exp,gemini-2.0-flash-thinking-exp-01-21,gemini-2.0-flash-thinking-exp-1219,gemini-2.0-pro-exp,gemini-2.0-pro-exp-02-05,gemini-2.5-flash,gemini-2.5-flash-lite-preview-06-17,gemini-2.5-flash-preview-04-17,gemini-2.5-flash-preview-04-17-thinking,gemini-2.5-flash-preview-05-20,gemini-2.5-flash-preview-tts,gemini-2.5-pro,gemini-2.5-pro-exp-03-25,gemini-2.5-pro-preview-03-25,gemini-2.5-pro-preview-05-06,gemini-2.5-pro-preview-06-05,gemini-2.5-pro-preview-tts,gemini-exp-1206,gemini-pro-vision,gemma-3-12b-it,gemma-3-1b-it,gemma-3-27b-it,gemma-3-4b-it,gemma-3n-e4b-it,learnlm-2.0-flash-experimental
openrouter = 01-ai/yi-large,aetherwiing/mn-starcannon-12b,agentica-org/deepcoder-14b-preview:free,ai21/jamba-1.6-large,ai21/jamba-1.6-mini,aion-labs/aion-1.0,aion-labs/aion-1.0-mini,aion-labs/aion-rp-llama-3.1-8b,alfredpros/codellama-7b-instruct-solidity,all-hands/openhands-lm-32b-v0.1,alpindale/goliath-120b,alpindale/magnum-72b,amazon/nova-lite-v1,amazon/nova-micro-v1,amazon/nova-pro-v1,anthracite-org/magnum-v2-72b,anthracite-org/magnum-v4-72b,anthropic/claude-2,anthropic/claude-2.0,anthropic/claude-2.0:beta,anthropic/claude-2.1,anthropic/claude-2.1:beta,anthropic/claude-2:beta,anthropic/claude-3-haiku,anthropic/claude-3-haiku:beta,anthropic/claude-3-opus,anthropic/claude-3-opus:beta,anthropic/claude-3-sonnet,anthropic/claude-3-sonnet:beta,anthropic/claude-3.5-haiku,anthropic/claude-3.5-haiku-20241022,anthropic/claude-3.5-haiku-20241022:beta,anthropic/claude-3.5-haiku:beta,anthropic/claude-3.5-sonnet,anthropic/claude-3.5-sonnet-20240620,anthropic/claude-3.5-sonnet-20240620:beta,anthropic/claude-3.5-sonnet:beta,anthropic/claude-3.7-sonnet,anthropic/claude-3.7-sonnet:beta,anthropic/claude-3.7-sonnet:thinking,anthropic/claude-opus-4,anthropic/claude-sonnet-4,arcee-ai/arcee-blitz,arcee-ai/caller-large,arcee-ai/coder-large,arcee-ai/maestro-reasoning,arcee-ai/spotlight,arcee-ai/virtuoso-large,arcee-ai/virtuoso-medium-v2,arliai/qwq-32b-arliai-rpr-v1:free,cognitivecomputations/dolphin-mixtral-8x22b,cognitivecomputations/dolphin3.0-mistral-24b:free,cognitivecomputations/dolphin3.0-r1-mistral-24b:free,cohere/command,cohere/command-a,cohere/command-r,cohere/command-r-03-2024,cohere/command-r-08-2024,cohere/command-r-plus,cohere/command-r-plus-04-2024,cohere/command-r-plus-08-2024,cohere/command-r7b-12-2024,deepseek/deepseek-chat,deepseek/deepseek-chat-v3-0324,deepseek/deepseek-chat-v3-0324:free,deepseek/deepseek-chat:free,deepseek/deepseek-prover-v2,deepseek/deepseek-r1,deepseek/deepseek-r1-0528,deepseek/deepseek-r1-0528-qwen3-8b,deepseek/deepseek-r1-0528-qwen3-8b:free,deepseek/deepseek-r1-0528:free,deepseek/deepseek-r1-distill-llama-70b,deepseek/deepseek-r1-distill-llama-70b:free,deepseek/deepseek-r1-distill-llama-8b,deepseek/deepseek-r1-distill-qwen-1.5b,deepseek/deepseek-r1-distill-qwen-14b,deepseek/deepseek-r1-distill-qwen-14b:free,deepseek/deepseek-r1-distill-qwen-32b,deepseek/deepseek-r1-distill-qwen-32b:free,deepseek/deepseek-r1-distill-qwen-7b,deepseek/deepseek-r1:free,deepseek/deepseek-v3-base:free,eleutherai/llemma_7b,eva-unit-01/eva-llama-3.33-70b,eva-unit-01/eva-qwen-2.5-32b,eva-unit-01/eva-qwen-2.5-72b,featherless/qwerky-72b:free,google/gemini-2.0-flash-001,google/gemini-2.0-flash-exp:free,google/gemini-2.0-flash-lite-001,google/gemini-2.5-flash,google/gemini-2.5-flash-lite-preview-06-17,google/gemini-2.5-flash-preview,google/gemini-2.5-flash-preview-05-20,google/gemini-2.5-flash-preview-05-20:thinking,google/gemini-2.5-flash-preview:thinking,google/gemini-2.5-pro,google/gemini-2.5-pro-exp-03-25,google/gemini-2.5-pro-preview,google/gemini-2.5-pro-preview-05-06,google/gemini-flash-1.5,google/gemini-flash-1.5-8b,google/gemini-pro-1.5,google/gemma-2-27b-it,google/gemma-2-9b-it,google/gemma-2-9b-it:free,google/gemma-3-12b-it,google/gemma-3-12b-it:free,google/gemma-3-1b-it:free,google/gemma-3-27b-it,google/gemma-3-27b-it:free,google/gemma-3-4b-it,google/gemma-3-4b-it:free,google/gemma-3n-e4b-it:free,gryphe/mythomax-l2-13b,inception/mercury-coder-small-beta,infermatic/mn-inferor-12b,inflection/inflection-3-pi,inflection/inflection-3-productivity,liquid/lfm-3b,liquid/lfm-40b,liquid/lfm-7b,mancer/weaver,meta-llama/llama-3-70b-instruct,meta-llama/llama-3-8b-instruct,meta-llama/llama-3.1-405b,meta-llama/llama-3.1-405b-instruct,meta-llama/llama-3.1-70b-instruct,meta-llama/llama-3.1-8b-instruct,meta-llama/llama-3.1-8b-instruct:free,meta-llama/llama-3.2-11b-vision-instruct,meta-llama/llama-3.2-11b-vision-instruct:free,meta-llama/llama-3.2-1b-instruct,meta-llama/llama-3.2-1b-instruct:free,meta-llama/llama-3.2-3b-instruct,meta-llama/llama-3.2-3b-instruct:free,meta-llama/llama-3.2-90b-vision-instruct,meta-llama/llama-3.3-70b-instruct,meta-llama/llama-3.3-70b-instruct:free,meta-llama/llama-3.3-8b-instruct:free,meta-llama/llama-4-maverick,meta-llama/llama-4-maverick:free,meta-llama/llama-4-scout,meta-llama/llama-4-scout:free,meta-llama/llama-guard-2-8b,meta-llama/llama-guard-3-8b,meta-llama/llama-guard-4-12b,microsoft/mai-ds-r1:free,microsoft/phi-3-medium-128k-instruct,microsoft/phi-3-mini-128k-instruct,microsoft/phi-3.5-mini-128k-instruct,microsoft/phi-4,microsoft/phi-4-multimodal-instruct,microsoft/phi-4-reasoning-plus,microsoft/phi-4-reasoning-plus:free,microsoft/phi-4-reasoning:free,microsoft/wizardlm-2-8x22b,minimax/minimax-01,minimax/minimax-m1,minimax/minimax-m1:extended,mistralai/codestral-2501,mistralai/devstral-small,mistralai/devstral-small:free,mistralai/magistral-medium-2506,mistralai/magistral-medium-2506:thinking,mistralai/magistral-small-2506,mistralai/ministral-3b,mistralai/ministral-8b,mistralai/mistral-7b-instruct,mistralai/mistral-7b-instruct-v0.1,mistralai/mistral-7b-instruct-v0.2,mistralai/mistral-7b-instruct-v0.3,mistralai/mistral-7b-instruct:free,mistralai/mistral-large,mistralai/mistral-large-2407,mistralai/mistral-large-2411,mistralai/mistral-medium,mistralai/mistral-medium-3,mistralai/mistral-nemo,mistralai/mistral-nemo:free,mistralai/mistral-saba,mistralai/mistral-small,mistralai/mistral-small-24b-instruct-2501,mistralai/mistral-small-24b-instruct-2501:free,mistralai/mistral-small-3.1-24b-instruct,mistralai/mistral-small-3.1-24b-instruct:free,mistralai/mistral-tiny,mistralai/mixtral-8x22b-instruct,mistralai/mixtral-8x7b-instruct,mistralai/pixtral-12b,mistralai/pixtral-large-2411,moonshotai/kimi-dev-72b:free,moonshotai/kimi-vl-a3b-thinking:free,neversleep/llama-3-lumimaid-70b,neversleep/llama-3-lumimaid-8b,neversleep/llama-3.1-lumimaid-70b,neversleep/llama-3.1-lumimaid-8b,neversleep/noromaid-20b,nothingiisreal/mn-celeste-12b,nousresearch/deephermes-3-llama-3-8b-preview:free,nousresearch/deephermes-3-mistral-24b-preview:free,nousresearch/hermes-2-pro-llama-3-8b,nousresearch/hermes-3-llama-3.1-405b,nousresearch/hermes-3-llama-3.1-70b,nousresearch/nous-hermes-2-mixtral-8x7b-dpo,nvidia/llama-3.1-nemotron-70b-instruct,nvidia/llama-3.1-nemotron-ultra-253b-v1,nvidia/llama-3.1-nemotron-ultra-253b-v1:free,nvidia/llama-3.3-nemotron-super-49b-v1,nvidia/llama-3.3-nemotron-super-49b-v1:free,open-r1/olympiccoder-32b:free,openai/chatgpt-4o-latest,openai/codex-mini,openai/gpt-3.5-turbo,openai/gpt-3.5-turbo-0125,openai/gpt-3.5-turbo-0613,openai/gpt-3.5-turbo-1106,openai/gpt-3.5-turbo-16k,openai/gpt-3.5-turbo-instruct,openai/gpt-4,openai/gpt-4-0314,openai/gpt-4-1106-preview,openai/gpt-4-turbo,openai/gpt-4-turbo-preview,openai/gpt-4.1,openai/gpt-4.1-mini,openai/gpt-4.1-nano,openai/gpt-4.5-preview,openai/gpt-4o,openai/gpt-4o-2024-05-13,openai/gpt-4o-2024-08-06,openai/gpt-4o-2024-11-20,openai/gpt-4o-mini,openai/gpt-4o-mini-2024-07-18,openai/gpt-4o-mini-search-preview,openai/gpt-4o-search-preview,openai/gpt-4o:extended,openai/o1,openai/o1-mini,openai/o1-mini-2024-09-12,openai/o1-preview,openai/o1-preview-2024-09-12,openai/o1-pro,openai/o3,openai/o3-mini,openai/o3-mini-high,openai/o3-pro,openai/o4-mini,openai/o4-mini-high,opengvlab/internvl3-14b:free,opengvlab/internvl3-2b:free,openrouter/auto,perplexity/llama-3.1-sonar-large-128k-online,perplexity/llama-3.1-sonar-small-128k-online,perplexity/r1-1776,perplexity/sonar,perplexity/sonar-deep-research,perplexity/sonar-pro,perplexity/sonar-reasoning,perplexity/sonar-reasoning-pro,pygmalionai/mythalion-13b,qwen/qwen-2-72b-instruct,qwen/qwen-2.5-72b-instruct,qwen/qwen-2.5-72b-instruct:free,qwen/qwen-2.5-7b-instruct,qwen/qwen-2.5-coder-32b-instruct,qwen/qwen-2.5-coder-32b-instruct:free,qwen/qwen-2.5-vl-7b-instruct,qwen/qwen-2.5-vl-7b-instruct:free,qwen/qwen-max,qwen/qwen-plus,qwen/qwen-turbo,qwen/qwen-vl-max,qwen/qwen-vl-plus,qwen/qwen2.5-vl-32b-instruct,qwen/qwen2.5-vl-32b-instruct:free,qwen/qwen2.5-vl-72b-instruct,qwen/qwen2.5-vl-72b-instruct:free,qwen/qwen3-14b,qwen/qwen3-14b:free,qwen/qwen3-235b-a22b,qwen/qwen3-235b-a22b:free,qwen/qwen3-30b-a3b,qwen/qwen3-30b-a3b:free,qwen/qwen3-32b,qwen/qwen3-32b:free,qwen/qwen3-8b,qwen/qwen3-8b:free,qwen/qwq-32b,qwen/qwq-32b-preview,qwen/qwq-32b:free,raifle/sorcererlm-8x22b,rekaai/reka-flash-3:free,sao10k/fimbulvetr-11b-v2,sao10k/l3-euryale-70b,sao10k/l3-lunaris-8b,sao10k/l3.1-euryale-70b,sao10k/l3.3-euryale-70b,sarvamai/sarvam-m:free,scb10x/llama3.1-typhoon2-70b-instruct,sentientagi/dobby-mini-unhinged-plus-llama-3.1-8b,shisa-ai/shisa-v2-llama3.3-70b:free,sophosympatheia/midnight-rose-70b,thedrummer/anubis-pro-105b-v1,thedrummer/rocinante-12b,thedrummer/skyfall-36b-v2,thedrummer/unslopnemo-12b,thedrummer/valkyrie-49b-v1,thudm/glm-4-32b,thudm/glm-4-32b:free,thudm/glm-z1-32b,thudm/glm-z1-32b:free,thudm/glm-z1-rumination-32b,tngtech/deepseek-r1t-chimera:free,undi95/remm-slerp-l2-13b,undi95/toppy-m-7b,x-ai/grok-2-1212,x-ai/grok-2-vision-1212,x-ai/grok-3-beta,x-ai/grok-3-mini-beta,x-ai/grok-beta,x-ai/grok-vision-beta
groq = allam-2-7b,compound-beta,compound-beta-mini,deepseek-r1-distill-llama-70b,distil-whisper-large-v3-en,gemma2-9b-it,llama-3.1-8b-instant,llama-3.3-70b-versatile,llama3-70b-8192,llama3-8b-8192,meta-llama/llama-4-maverick-17b-128e-instruct,meta-llama/llama-4-scout-17b-16e-instruct,meta-llama/llama-guard-4-12b,meta-llama/llama-prompt-guard-2-22m,meta-llama/llama-prompt-guard-2-86m,mistral-saba-24b,playai-tts,playai-tts-arabic,qwen-qwq-32b,qwen/qwen3-32b,whisper-large-v3,whisper-large-v3-turbo
local = gemma-3-12b-it,qwen/qwen2.5-vl-7b,text-embedding-nomic-embed-text-v1.5
anthropic = claude-2.0,claude-2.1,claude-3-5-haiku-20241022,claude-3-5-sonnet-20240620,claude-3-5-sonnet-20241022,claude-3-7-sonnet-20250219,claude-3-haiku-20240307,claude-3-opus-20240229,claude-3-sonnet-20240229,claude-instant-1.2

[SHOPIFY]
shop_url = https://sqt3xp-df.myshopify.com
api_token = 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
connection_ok = true
upload_meta_description = True

[WORDPRESS]
site_url = 
username = 
upload_meta_description = False
password = 

[DATABASES]
db1_name = Default Content Database
db1_path = content_ledger.db
db_1750597901_e3cd1f66_name = test
db_1750597901_e3cd1f66_path = C:/Users/<USER>/Documents/Code/Seo Assistant/tester111.db

[APPLICATION_SETTINGS]
active_db_key = db1
default_db_path = content_ledger.db

