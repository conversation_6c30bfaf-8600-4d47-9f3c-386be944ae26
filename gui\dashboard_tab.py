"""
Dashboard Tab - Content Strategist Dashboard

This module contains the DashboardTab class which handles the main dashboard
interface including job planning, execution controls, manual entry, and status display.
"""

import tkinter as tk
from tkinter import ttk, messagebox
import threading
import json
from typing import Dict, Any, Optional

from core import config_manager
from core.llm_interface import AVAILABLE_MODELS


class DashboardTab(ttk.Frame):
    """
    Dashboard tab containing job planning, execution controls, manual entry, and status display.
    """

    def __init__(self, master, main_app, db, planner, worker, log_func, *args, **kwargs):
        super().__init__(master, *args, **kwargs)
        
        # Store references to main app components
        self.main_app = main_app
        self.db = db
        self.planner = planner
        self.worker = worker
        self.log_message = log_func
        
        # Initialize variables
        self.current_planned_job = None
        self.last_job_execution_result = None
        self.is_running = False
        self.stop_requested = False
        self.current_thread = None
        
        # Execution control variables
        self.run_data_collection_var = tk.BooleanVar(value=True)
        self.run_blog_writing_var = tk.BooleanVar(value=True)
        self.run_publish_var = tk.BooleanVar(value=True)
        self.include_product_links_var = tk.BooleanVar(value=True)
        
        # Batch automation variables
        self.num_batch_jobs_var = tk.IntVar(value=1)
        self.interval_value_var = tk.IntVar(value=10)
        self.interval_unit_var = tk.StringVar(value="minutes")
        self.is_batch_running = False
        self.batch_current_job = 0
        self.batch_total_jobs = 0

        
        # Data source variables
        self.use_serpapi_var = tk.BooleanVar(value=True)
        self.use_alsoasked_var = tk.BooleanVar(value=False)
        self.use_llm_suggestion_var = tk.BooleanVar(value=False)
        
        # Publishing variables
        self.publish_platform_var = tk.StringVar(value="Shopify")
        self.publish_as_draft_var = tk.BooleanVar(value=True)
        
        # LLM selection variables
        self.writing_llm_provider_var = tk.StringVar()
        self.writing_llm_model_var = tk.StringVar()
        
        # Manual entry variables
        self.manual_keyword_var = tk.StringVar()
        self.manual_craft_var = tk.StringVar()
        self.manual_pillar_var = tk.StringVar()
        self.manual_angle_var = tk.StringVar()
        self.craft_pillar_mapping = {}
        
        # Create the dashboard UI
        self._create_widgets()
        
        # Initialize state
        self.reset_execution_controls()
        self.populate_manual_entry_dropdowns()
        self.update_dashboard_status()

    def _create_widgets(self):
        """Create the dashboard tab widgets with improved layout."""
        # Configure padding
        self.configure(padding="15")
        
        # Title
        title_label = ttk.Label(self, text="Content Strategist Dashboard",
                               font=("Arial", 16, "bold"))
        title_label.pack(pady=(0, 15))

        # Top row: Planning and Execution side by side
        top_row = ttk.Frame(self)
        top_row.pack(fill=tk.BOTH, expand=True, pady=(0, 15))

        # Left side: Job Planning
        planning_frame = ttk.LabelFrame(top_row, text="📋 Job Planning", padding="12")
        planning_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 8))

        # Plan job controls
        plan_controls = ttk.Frame(planning_frame)
        plan_controls.pack(fill=tk.X, pady=(0, 10))

        self.plan_job_button = ttk.Button(plan_controls, text="🔎 Plan Next Job",
                                        command=self.plan_next_job)
        self.plan_job_button.pack(side=tk.LEFT, padx=(0, 8))

        self.stop_button = ttk.Button(plan_controls, text="🛑 Stop",
                                     command=self.stop_operations, state=tk.DISABLED)
        self.stop_button.pack(side=tk.LEFT, padx=(0, 8))

        # Progress bar for planning
        self.job_progress = ttk.Progressbar(plan_controls, mode='indeterminate', length=150)

        # Next job info (expandable)
        ttk.Label(planning_frame, text="Top Auto-Candidate (from NEW/ON_HOLD):", font="-weight bold").pack(anchor=tk.W, pady=(8, 2))
        self.next_job_info = tk.Text(planning_frame, height=6, wrap=tk.WORD, state=tk.DISABLED)
        self.next_job_info.pack(fill=tk.BOTH, expand=True)

        # Right side: Job Execution
        self.execution_frame = ttk.LabelFrame(top_row, text="🚀 Job Execution", padding="12")
        self.execution_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=(8, 0))

        # Create execution controls
        self._create_execution_controls()

        # Automation row: Batch Processing
        automation_frame = ttk.LabelFrame(top_row, text="🤖 Batch Automation", padding="12")
        automation_frame.pack(side=tk.BOTTOM, fill=tk.X, pady=(8, 0))
        
        self._create_automation_controls(automation_frame)

        # Middle row: Manual Entry (horizontal layout)
        manual_frame = ttk.LabelFrame(self, text="➕ Quick Add Content", padding="12")
        manual_frame.pack(fill=tk.X, pady=(0, 15))
        
        self._create_manual_entry_section(manual_frame)

        # Bottom row: Statistics (expandable)
        stats_frame = ttk.LabelFrame(self, text="📊 Database Status", padding="10")
        stats_frame.pack(fill=tk.BOTH, expand=True)

        self.stats_text = tk.Text(stats_frame, height=8, wrap=tk.WORD, state=tk.DISABLED)
        self.stats_text.pack(fill=tk.BOTH, expand=True)

    def _create_manual_entry_section(self, parent):
        """Create the manual entry section with improved responsive layout."""
        # Main grid
        entry_grid = ttk.Frame(parent)
        entry_grid.pack(fill=tk.X, pady=(0, 8))

        # Configure grid weights for proper expansion
        entry_grid.columnconfigure(1, weight=2)  # Keyword entry gets more space
        entry_grid.columnconfigure(3, weight=1)  # Craft combo expandable
        entry_grid.columnconfigure(5, weight=1)  # Pillar combo expandable

        # Row 1: Keyword, Craft, and Pillar
        ttk.Label(entry_grid, text="Keyword:").grid(row=0, column=0, sticky="w", padx=(0, 8), pady=5)
        ttk.Entry(entry_grid, textvariable=self.manual_keyword_var).grid(row=0, column=1, sticky="ew", padx=(0, 15), pady=5)

        ttk.Label(entry_grid, text="Craft:").grid(row=0, column=2, sticky="w", padx=(0, 8), pady=5)
        self.manual_craft_combo = ttk.Combobox(entry_grid, textvariable=self.manual_craft_var, state="readonly")
        self.manual_craft_combo.grid(row=0, column=3, sticky="ew", padx=(0, 15), pady=5)
        self.manual_craft_combo.bind('<<ComboboxSelected>>', self.on_manual_craft_changed)

        ttk.Label(entry_grid, text="Pillar:").grid(row=0, column=4, sticky="w", padx=(0, 8), pady=5)
        self.manual_pillar_combo = ttk.Combobox(entry_grid, textvariable=self.manual_pillar_var, state="readonly")
        self.manual_pillar_combo.grid(row=0, column=5, sticky="ew", pady=5)

        # Row 2: Angle and Add button
        ttk.Label(entry_grid, text="Angle:").grid(row=1, column=0, sticky="w", padx=(0, 8), pady=5)
        angle_entry = ttk.Entry(entry_grid, textvariable=self.manual_angle_var)
        angle_entry.grid(row=1, column=1, columnspan=4, sticky="ew", padx=(0, 15), pady=5)

        ttk.Button(entry_grid, text="➕ Add", command=self.add_manual_content_idea).grid(row=1, column=5, sticky="ew", pady=5)

    def _create_execution_controls(self):
        """Create the execution controls section with compact layout."""
        # Instructions
        instruction_label = ttk.Label(self.execution_frame,
                                     text="Plan a job first to enable execution controls.",
                                     font=("Arial", 9), foreground="gray")
        instruction_label.pack(pady=(0, 8))
        
        # Job Primed for Execution label
        ttk.Label(self.execution_frame, text="Job Primed for Execution:", font="-weight bold").pack(anchor=tk.W, pady=(0, 2))
        
        # Planned job details (expandable)
        self.planned_job_text = tk.Text(self.execution_frame, height=4, wrap=tk.WORD, state=tk.DISABLED)
        self.planned_job_text.pack(fill=tk.BOTH, expand=True, pady=(0, 8))

        # Execution options in a responsive grid
        options_frame = ttk.Frame(self.execution_frame)
        options_frame.pack(fill=tk.X, pady=(0, 8))

        # Configure grid weights for proper expansion
        options_frame.columnconfigure(1, weight=1)  # Settings column expands

        # Column 1: Steps
        steps_frame = ttk.LabelFrame(options_frame, text="Steps", padding="8")
        steps_frame.grid(row=0, column=0, sticky="nsew", padx=(0, 8))

        ttk.Checkbutton(steps_frame, text="SERP", variable=self.run_data_collection_var).pack(anchor=tk.W)
        ttk.Checkbutton(steps_frame, text="Write", variable=self.run_blog_writing_var).pack(anchor=tk.W)
        ttk.Checkbutton(steps_frame, text="Publish", variable=self.run_publish_var).pack(anchor=tk.W)

        # Column 2: Source & Platform
        settings_frame = ttk.LabelFrame(options_frame, text="Settings", padding="8")
        settings_frame.grid(row=0, column=1, sticky="nsew")

        # Configure settings grid for proper expansion
        settings_frame.columnconfigure(1, weight=1)  # Dropdown column expands

        # Data sources section
        ttk.Label(settings_frame, text="Data Sources:").grid(row=0, column=0, sticky="w", padx=(0, 8), pady=3)
        sources_subframe = ttk.Frame(settings_frame)
        sources_subframe.grid(row=0, column=1, sticky="w", pady=3)
        
        ttk.Checkbutton(sources_subframe, text="SerpApi", variable=self.use_serpapi_var).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Checkbutton(sources_subframe, text="AlsoAsked", variable=self.use_alsoasked_var).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Checkbutton(sources_subframe, text="LLM", variable=self.use_llm_suggestion_var).pack(side=tk.LEFT)

        # Platform row
        ttk.Label(settings_frame, text="Platform:").grid(row=1, column=0, sticky="w", padx=(0, 8), pady=3)
        platform_combo = ttk.Combobox(settings_frame, textvariable=self.publish_platform_var, state="readonly")
        platform_combo['values'] = ["Shopify", "WordPress"]
        platform_combo.grid(row=1, column=1, sticky="ew", pady=3)
        
        # Draft checkbox row
        ttk.Checkbutton(settings_frame, text="Publish as Draft", variable=self.publish_as_draft_var).grid(row=2, column=0, columnspan=2, sticky="w", pady=3)

        # Product linking checkbox row
        ttk.Checkbutton(settings_frame, text="🔗 Include Product Links", variable=self.include_product_links_var).grid(row=3, column=0, columnspan=2, sticky="w", pady=3)

        # LLM selection row - using a sub-frame for provider and model
        ttk.Label(settings_frame, text="LLM:").grid(row=4, column=0, sticky="w", padx=(0, 8), pady=3)
        llm_frame = ttk.Frame(settings_frame)
        llm_frame.grid(row=4, column=1, sticky="ew", pady=3)
        llm_frame.columnconfigure(1, weight=1)  # Model combo expands more

        self.writing_provider_combo = ttk.Combobox(llm_frame, textvariable=self.writing_llm_provider_var, state="readonly", width=10)
        self.writing_provider_combo.grid(row=0, column=0, sticky="w", padx=(0, 5))
        self.writing_provider_combo.bind('<<ComboboxSelected>>', self.on_writing_provider_changed)

        self.writing_model_combo = ttk.Combobox(llm_frame, textvariable=self.writing_llm_model_var, state="readonly")
        self.writing_model_combo.grid(row=0, column=1, sticky="ew")

        # Execute button and View Last Job Output button
        execute_frame = ttk.Frame(self.execution_frame)
        execute_frame.pack(pady=8)

        self.execute_job_button = ttk.Button(execute_frame, text="🚀 Execute Job",
                                           command=self.execute_planned_job, state=tk.DISABLED)
        self.execute_job_button.pack(side=tk.LEFT, padx=(0, 8))

        self.view_last_job_button = ttk.Button(execute_frame, text="📋 View Last Job Output",
                                             command=self.view_last_job_output, state=tk.DISABLED)
        self.view_last_job_button.pack(side=tk.LEFT, padx=(0, 8))

        # Progress bar for execution (initially hidden)
        self.execution_progress = ttk.Progressbar(execute_frame, mode='indeterminate', length=150)

    def _create_automation_controls(self, parent):
        """Create the batch automation controls."""
        # Automation status display
        self.automation_status_label = ttk.Label(parent, text="Ready for batch automation", 
                                                font=("Arial", 9), foreground="blue")
        self.automation_status_label.pack(pady=(0, 8))
        
        # Automation controls grid
        controls_grid = ttk.Frame(parent)
        controls_grid.pack(fill=tk.X, pady=(0, 8))
        
        # Configure grid weights
        controls_grid.columnconfigure(1, weight=1)
        controls_grid.columnconfigure(3, weight=1)
        
        # Number of jobs
        ttk.Label(controls_grid, text="Jobs to run:").grid(row=0, column=0, sticky="w", padx=(0, 8), pady=5)
        ttk.Spinbox(controls_grid, from_=1, to=100, textvariable=self.num_batch_jobs_var, width=8).grid(
            row=0, column=1, sticky="w", pady=5)
        
        # Interval between jobs
        ttk.Label(controls_grid, text="Interval:").grid(row=0, column=2, sticky="w", padx=(20, 8), pady=5)
        interval_frame = ttk.Frame(controls_grid)
        interval_frame.grid(row=0, column=3, sticky="w", pady=5)
        
        ttk.Spinbox(interval_frame, from_=1, to=360, textvariable=self.interval_value_var, width=8).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Combobox(interval_frame, textvariable=self.interval_unit_var, values=["minutes", "hours"], 
                    width=8, state="readonly").pack(side=tk.LEFT)
        

        
        # Automation buttons and progress
        automation_buttons_frame = ttk.Frame(parent)
        automation_buttons_frame.pack(pady=8)
        
        self.start_automation_button = ttk.Button(automation_buttons_frame, text="🚀 Start Automated Batch",
                                                 command=self.start_automated_batch)
        self.start_automation_button.pack(side=tk.LEFT, padx=(0, 8))
        
        self.stop_automation_button = ttk.Button(automation_buttons_frame, text="🛑 Stop Automation",
                                               command=self.stop_automated_batch, state=tk.DISABLED)
        self.stop_automation_button.pack(side=tk.LEFT, padx=(0, 8))
        
        # Progress bar for automation (initially hidden)
        self.automation_progress = ttk.Progressbar(automation_buttons_frame, mode='indeterminate', length=200)

    def populate_llm_dropdowns(self):
        """Populate LLM provider and model dropdowns."""
        try:
            # Get available providers
            providers = list(AVAILABLE_MODELS.keys())

            # Populate provider dropdown
            self.writing_provider_combo['values'] = providers

            # Set default provider from config
            default_writing_provider = config_manager.get_pipeline_setting('writing_llm') or 'local'

            if default_writing_provider in providers:
                self.writing_llm_provider_var.set(default_writing_provider)
                self.on_writing_provider_changed()

        except Exception as e:
            self.log_message(f"Error populating LLM dropdowns: {e}", "error")

    def on_writing_provider_changed(self, event=None):
        """Handle writing LLM provider selection change."""
        try:
            provider = self.writing_llm_provider_var.get()
            if provider and provider in AVAILABLE_MODELS:
                models = AVAILABLE_MODELS[provider]
                self.writing_model_combo['values'] = models

                # Set default model
                default_model = config_manager.get_pipeline_setting('writing_llm_model')
                if default_model and default_model in models:
                    self.writing_llm_model_var.set(default_model)
                elif models:
                    self.writing_llm_model_var.set(models[0])

        except Exception as e:
            self.log_message(f"Error updating writing models: {e}", "error")

    def populate_manual_entry_dropdowns(self):
        """Populate the craft and pillar dropdowns for manual entry."""
        try:
            # Get business pillars from config
            business_pillars = config_manager.get_pipeline_setting('business_pillars') or ""

            # Parse business pillars to extract crafts and their pillars
            crafts = []
            self.craft_pillar_mapping = {}

            for line in business_pillars.split('\n'):
                line = line.strip()
                if '=' in line:
                    craft, pillars_text = line.split('=', 1)
                    craft = craft.strip()
                    pillars = [p.strip() for p in pillars_text.split(',') if p.strip()]

                    if craft and pillars:
                        crafts.append(craft)
                        self.craft_pillar_mapping[craft] = pillars

            # Populate craft dropdown
            self.manual_craft_combo['values'] = crafts

            # Clear pillar dropdown initially
            self.manual_pillar_combo['values'] = []

        except Exception as e:
            self.log_message(f"Error populating manual entry dropdowns: {e}", "error")

    def on_manual_craft_changed(self, event=None):
        """Handle craft selection change in manual entry."""
        try:
            selected_craft = self.manual_craft_var.get()
            if selected_craft in self.craft_pillar_mapping:
                pillars = self.craft_pillar_mapping[selected_craft]
                self.manual_pillar_combo['values'] = pillars
                self.manual_pillar_var.set(pillars[0] if pillars else "")
            else:
                self.manual_pillar_combo['values'] = []
                self.manual_pillar_var.set("")
        except Exception as e:
            self.log_message(f"Error updating pillar dropdown: {e}", "error")

    def add_manual_content_idea(self):
        """Add a manually entered content idea to the database."""
        try:
            # Get input values
            keyword = self.manual_keyword_var.get().strip()
            craft = self.manual_craft_var.get().strip()
            pillar = self.manual_pillar_var.get().strip()
            angle = self.manual_angle_var.get().strip()

            # Validate inputs
            if not keyword:
                messagebox.showwarning("Missing Information", "Please enter a keyword.")
                return

            if not craft:
                messagebox.showwarning("Missing Information", "Please select a craft.")
                return

            if not pillar:
                messagebox.showwarning("Missing Information", "Please select a pillar.")
                return

            # Check if keyword already exists
            existing_content = self.db.get_content_by_keyword(keyword)
            if existing_content:
                messagebox.showwarning("Duplicate Keyword", f"Content with keyword '{keyword}' already exists.")
                return

            # Add to database
            content_id = self.db.add_content_idea(
                keyword=keyword,
                pillar=pillar,
                craft=craft,
                proposed_angle=angle or None,
                status='NEW'
            )

            self.log_message(f"✅ Added manual content idea: '{keyword}' (ID: {content_id})", "success")

            # Clear form
            self.manual_keyword_var.set("")
            self.manual_craft_var.set("")
            self.manual_pillar_var.set("")
            self.manual_angle_var.set("")

            # Refresh displays
            self.main_app.refresh_content_plan()
            self.update_dashboard_status()

        except Exception as e:
            self.log_message(f"Error adding manual content idea: {e}", "error")
            messagebox.showerror("Error", f"Failed to add content idea: {e}")

    def stop_operations(self):
        """Stop any running operations."""
        if self.is_running or self.is_batch_running:
            self.stop_requested = True
            if self.is_batch_running:
                self.log_message("🛑 Stop requested for batch automation - will terminate after current job...", "warning")
                self.stop_automation_button.config(state=tk.DISABLED)
                self.automation_status_label.config(text="Stopping after current job...", foreground="red")
            else:
                self.log_message("🛑 Stop requested - operations will terminate soon...", "warning")
            self.stop_button.config(state=tk.DISABLED)
        else:
            messagebox.showinfo("No Operations", "No operations are currently running.")

    def update_dashboard_status(self):
        """Update the dashboard status displays."""
        try:
            # Get database statistics
            stats = self.db.get_content_stats()

            # Update stats display
            stats_text = "Content Database Status:\n\n"
            total_content = sum(stats.values())

            for status, count in stats.items():
                percentage = (count / total_content * 100) if total_content > 0 else 0
                stats_text += f"• {status}: {count} ({percentage:.1f}%)\n"

            stats_text += f"\nTotal Content Ideas: {total_content}"

            self.stats_text.config(state=tk.NORMAL)
            self.stats_text.delete('1.0', tk.END)
            self.stats_text.insert('1.0', stats_text)
            self.stats_text.config(state=tk.DISABLED)

            # Update next job info
            self.update_next_job_info()

        except Exception as e:
            self.log_message(f"Error updating dashboard status: {e}", "error")

    def update_next_job_info(self):
        """Update the next job information display."""
        try:
            # Get business logic settings
            freshness_threshold = float(config_manager.get_pipeline_setting('freshness_threshold') or 70.0)
            pillar_weights_str = config_manager.get_pipeline_setting('pillar_weights') or '{}'
            try:
                pillar_weights = json.loads(pillar_weights_str)
            except json.JSONDecodeError:
                pillar_weights = {}  # Fallback to empty dict

            # Get top candidate without selecting it
            scored_content = self.planner.calculate_freshness_scores(pillar_weights)

            if scored_content and scored_content[0]['freshness_score'] >= freshness_threshold:
                top_candidate = scored_content[0]
                # Store the next job candidate ID for highlighting
                self.main_app.next_job_candidate_id = top_candidate['id']
                job_text = f"""Ready to Execute:
Keyword: {top_candidate['keyword']}
Pillar: {top_candidate['pillar']} | Craft: {top_candidate['craft']}
Freshness Score: {top_candidate['freshness_score']:.1f} (Threshold: {freshness_threshold:.1f})
Angle: {top_candidate.get('proposed_angle', 'N/A')[:100]}..."""
            else:
                self.main_app.next_job_candidate_id = None
                if scored_content:
                    best_score = scored_content[0]['freshness_score']
                    job_text = f"""No job meets threshold:
Best candidate score: {best_score:.1f} (Threshold: {freshness_threshold:.1f})
Suggestion: Generate more ideas or lower threshold in Settings"""
                else:
                    job_text = """No content available:
Generate new ideas using the Idea Generator tab"""

            self.next_job_info.config(state=tk.NORMAL)
            self.next_job_info.delete('1.0', tk.END)
            self.next_job_info.insert('1.0', job_text)
            self.next_job_info.config(state=tk.DISABLED)

        except Exception as e:
            self.log_message(f"Error updating next job info: {e}", "error")

    def plan_next_job(self):
        """Plan the next content job (select and mark as PLANNED)."""
        if self.is_running:
            messagebox.showwarning("Operation Running", "An operation is already running. Please wait for it to complete.")
            return

        def plan_thread():
            try:
                # Set running state and update UI
                self.is_running = True
                self.stop_requested = False
                self.master.after(0, lambda: self.plan_job_button.config(state=tk.DISABLED, text="Planning Job..."))
                self.master.after(0, lambda: self.stop_button.config(state=tk.NORMAL))
                self.master.after(0, lambda: self.job_progress.pack(side=tk.LEFT, padx=(0, 10)))
                self.master.after(0, lambda: self.job_progress.start(10))
                self.master.after(0, lambda: self.main_app.set_status_bar_text("Working...", True))

                self.log_message("🔎 Planning next content job...", "info")

                # Check for stop request
                if self.stop_requested:
                    self.log_message("🛑 Job planning stopped by user", "warning")
                    return

                # Get business logic settings
                freshness_threshold = float(config_manager.get_pipeline_setting('freshness_threshold') or 70.0)
                pillar_weights_str = config_manager.get_pipeline_setting('pillar_weights') or '{}'
                try:
                    pillar_weights = json.loads(pillar_weights_str)
                except json.JSONDecodeError:
                    pillar_weights = {}

                # Check for stop request
                if self.stop_requested:
                    self.log_message("🛑 Job planning stopped by user", "warning")
                    return

                # Get the next job using the planner
                next_job = self.planner.get_next_job(pillar_weights, freshness_threshold)

                if next_job:
                    # Store the planned job
                    self.current_planned_job = next_job

                    self.log_message(f"✅ Planned job: '{next_job['keyword']}' (Score: {next_job['freshness_score']:.1f})", "success")

                    # Update UI to show execution controls
                    self.master.after(0, self.show_execution_controls)

                    # Update displays
                    self.master.after(0, self.main_app.refresh_content_plan)
                    self.master.after(0, self.update_dashboard_status)
                else:
                    self.log_message("❌ No suitable job found. Try generating more ideas or lowering the freshness threshold.", "warning")
                    messagebox.showinfo("No Job Available",
                                      "No content meets the freshness threshold.\n\n"
                                      "Suggestions:\n"
                                      "• Generate more content ideas\n"
                                      "• Lower the freshness threshold in Settings")

            except Exception as e:
                self.log_message(f"❌ Error planning job: {e}", "error")
            finally:
                # Reset running state and UI
                self.is_running = False
                self.stop_requested = False
                self.master.after(0, lambda: self.plan_job_button.config(state=tk.NORMAL, text="🔎 Plan Next Job"))
                self.master.after(0, lambda: self.stop_button.config(state=tk.DISABLED))
                self.master.after(0, lambda: self.job_progress.stop())
                self.master.after(0, lambda: self.job_progress.pack_forget())
                self.master.after(0, lambda: self.main_app.set_status_bar_text("Ready"))

        self.current_thread = threading.Thread(target=plan_thread, daemon=True)
        self.current_thread.start()

    def show_execution_controls(self):
        """Show the execution controls section with planned job details."""
        if not self.current_planned_job:
            return

        # Update planned job details (compact format)
        job_text = f"Keyword: {self.current_planned_job['keyword']}\n"
        job_text += f"Pillar: {self.current_planned_job['pillar']} | Craft: {self.current_planned_job['craft']}\n"
        job_text += f"Score: {self.current_planned_job['freshness_score']:.1f}"

        self.planned_job_text.config(state=tk.NORMAL)
        self.planned_job_text.delete('1.0', tk.END)
        self.planned_job_text.insert('1.0', job_text)
        self.planned_job_text.config(state=tk.DISABLED)

        # Enable execution controls
        self.execute_job_button.config(state=tk.NORMAL)

    def reset_execution_controls(self):
        """Reset the execution controls to their initial state."""
        # Clear planned job details
        self.planned_job_text.config(state=tk.NORMAL)
        self.planned_job_text.delete('1.0', tk.END)
        self.planned_job_text.insert('1.0', "No job planned. Use 'Plan Next Job' to select a job.")
        self.planned_job_text.config(state=tk.DISABLED)

        # Disable execution controls
        self.execute_job_button.config(state=tk.DISABLED)
        self.view_last_job_button.config(state=tk.DISABLED)

        # Clear current planned job
        self.current_planned_job = None

    def execute_planned_job(self):
        """Execute the planned job with selected options."""
        if not self.current_planned_job:
            messagebox.showwarning("No Job Planned", "Please plan a job first using 'Plan Next Job'.")
            return

        if self.is_running:
            messagebox.showwarning("Operation Running", "An operation is already running. Please wait for it to complete.")
            return

        # Get the product linking toggle state
        include_links = self.include_product_links_var.get()

        # Delegate to main app's execute_planned_job method with product linking state
        self.main_app.execute_planned_job(include_product_links=include_links)

    def view_last_job_output(self):
        """View the output from the last job execution."""
        # Delegate to main app's view_last_job_output method
        self.main_app.view_last_job_output()
    
    def start_automated_batch(self):
        """Start automated batch processing."""
        if self.is_running or self.is_batch_running:
            messagebox.showwarning("Operation Running", "An operation is already running. Please wait for it to complete.")
            return
            
        def batch_thread():
            try:
                # Set batch running state
                self.is_batch_running = True
                self.is_running = True
                self.stop_requested = False
                
                # Get batch parameters
                num_jobs_to_run = self.num_batch_jobs_var.get()
                interval_value = self.interval_value_var.get()
                interval_unit = self.interval_unit_var.get()
                
                # Calculate interval in seconds
                if interval_unit == "minutes":
                    interval_seconds = interval_value * 60
                else:  # hours
                    interval_seconds = interval_value * 3600
                
                # Update UI for batch mode
                self.master.after(0, lambda: self.start_automation_button.config(state=tk.DISABLED))
                self.master.after(0, lambda: self.stop_automation_button.config(state=tk.NORMAL))
                self.master.after(0, lambda: self.automation_progress.pack(side=tk.LEFT, padx=(0, 10)))
                self.master.after(0, lambda: self.automation_progress.start(10))
                self.master.after(0, lambda: self.main_app.set_status_bar_text("Working...", True))
                
                # Store batch progress
                self.batch_total_jobs = num_jobs_to_run
                self.batch_current_job = 0
                
                self.log_message(f"🤖 Starting automated batch: {num_jobs_to_run} jobs, interval {interval_value} {interval_unit}.", "info")
                
                # Main batch loop
                for i in range(num_jobs_to_run):
                    if self.stop_requested:
                        self.log_message("🤖 Automated batch stopped by user.", "warning")
                        break
                    
                    self.batch_current_job = i + 1
                    self.master.after(0, lambda: self.automation_status_label.config(
                        text=f"Processing job {self.batch_current_job} of {self.batch_total_jobs}...", foreground="green"))
                    
                    self.log_message(f"🤖 Processing job {i+1} of {num_jobs_to_run}...", "info")
                    
                    # Step 1: Plan the job (using existing Plan Next Job logic)
                    self.log_message("🤖 Step A: Selecting next job for batch...", "info")
                    
                    # Get settings for job selection
                    freshness_threshold = float(config_manager.get_pipeline_setting('freshness_threshold') or 70.0)
                    pillar_weights_str = config_manager.get_pipeline_setting('pillar_weights') or '{}'
                    try:
                        pillar_weights = json.loads(pillar_weights_str)
                    except json.JSONDecodeError:
                        pillar_weights = {}
                    
                    # Select next job using planner
                    selected_job_for_batch = self.planner.select_next_job(pillar_weights, freshness_threshold)
                    
                    if not selected_job_for_batch:
                        self.log_message(f"🤖 No suitable job found for iteration {i+1}. Stopping batch.", "warning")
                        break
                    
                    self.current_planned_job = selected_job_for_batch
                    self.log_message(f"🤖 Primed for batch execution: '{self.current_planned_job['keyword']}'", "info")
                    
                    # Update execution panel on GUI
                    self.master.after(0, self.show_execution_controls)
                    
                    # Step 2: Execute the planned job
                    self.log_message(f"🤖 Step B: Executing job '{selected_job_for_batch['keyword']}'...", "info")
                    
                    # Get execution parameters from GUI
                    run_data_collection = self.run_data_collection_var.get()
                    run_blog_writing = self.run_blog_writing_var.get()
                    run_publish = self.run_publish_var.get()
                    use_serpapi = self.use_serpapi_var.get()
                    use_alsoasked = self.use_alsoasked_var.get()
                    use_llm_suggestion = self.use_llm_suggestion_var.get()
                    platform = self.publish_platform_var.get()
                    publish_as_draft = self.publish_as_draft_var.get()
                    writing_provider = self.writing_llm_provider_var.get()
                    writing_model = self.writing_llm_model_var.get()
                    
                    # Prepare job copy with overrides
                    job_copy = self.current_planned_job.copy()
                    if writing_provider and writing_model:
                        job_copy['override_writing_llm'] = writing_provider
                        job_copy['override_writing_model'] = writing_model
                        job_copy['override_analysis_llm'] = writing_provider 
                        job_copy['override_analysis_model'] = writing_model
                    
                    # Get product linking state
                    include_links = self.include_product_links_var.get()

                    # Prepare product list string based on toggle
                    if include_links:
                        # Make sure the product cache is populated
                        self.main_app.fetch_and_cache_stuga_products()
                        product_list_from_cache = self.main_app.stuga_product_list_cache
                        if product_list_from_cache and "Error" not in product_list_from_cache and "unavailable" not in product_list_from_cache:
                            stuga_product_list_str_for_prompt = product_list_from_cache
                        else:
                            stuga_product_list_str_for_prompt = "Product list data is currently unavailable. No product links can be generated."
                            self.log_message("Warning: Product linking enabled, but product list cache is empty or contains an error.", "warning")
                    else:
                        stuga_product_list_str_for_prompt = "Product linking is disabled for this article. Do not attempt to include product links."

                    # Execute the job
                    execution_result = self.worker.execute_job(
                        job_copy,
                        stuga_product_list_str=stuga_product_list_str_for_prompt,
                        run_data_collection=run_data_collection,
                        run_blog_writing=run_blog_writing,
                        run_publish=run_publish,
                        use_serpapi=use_serpapi,
                        use_alsoasked=use_alsoasked,
                        use_llm_suggestion=use_llm_suggestion,
                        publish_platform=platform,
                        publish_as_draft=publish_as_draft
                    )
                    
                    self.last_job_execution_result = execution_result
                    
                    if execution_result['success']:
                        self.log_message(f"🤖 Job {i+1} ('{job_copy['keyword']}') completed successfully.", "success")
                        if execution_result.get('platform_url'):
                            self.log_message(f"Published at: {execution_result['platform_url']}", "info")
                    else:
                        self.log_message(f"🤖 Job {i+1} ('{job_copy['keyword']}') failed: {'; '.join(execution_result['errors'])}", "error")
                    
                    # Update main GUI after each job completion
                    self.master.after(0, self.main_app.refresh_content_plan)
                    self.master.after(0, self.main_app.update_dashboard_status)
                    self.master.after(0, self.reset_execution_controls)
                    
                    # Wait for interval if not the last job
                    if i < num_jobs_to_run - 1:
                        self.master.after(0, lambda: self.automation_status_label.config(
                            text=f"Waiting {interval_value} {interval_unit} before next job...", foreground="orange"))
                        self.log_message(f"🤖 Waiting for {interval_value} {interval_unit} before next job...", "info")
                        
                        # Interruptible sleep
                        for _ in range(interval_seconds):
                            if self.stop_requested:
                                break
                            import time
                            time.sleep(1)
                        
                        if self.stop_requested:
                            self.log_message("🤖 Automated batch sleep interrupted by user.", "warning")
                            break
                
                self.log_message("🤖 Automated batch finished.", "info")
                
            except Exception as e:
                self.log_message(f"🤖 Error in automated batch: {e}", "error")
                import traceback
                print(f"Batch automation error: {traceback.format_exc()}")
            finally:
                # Reset batch state and UI
                self.is_batch_running = False
                self.is_running = False
                self.stop_requested = False
                self.batch_current_job = 0
                self.batch_total_jobs = 0
                
                self.master.after(0, lambda: self.start_automation_button.config(state=tk.NORMAL))
                self.master.after(0, lambda: self.stop_automation_button.config(state=tk.DISABLED))
                self.master.after(0, lambda: self.automation_progress.stop())
                self.master.after(0, lambda: self.automation_progress.pack_forget())
                self.master.after(0, lambda: self.automation_status_label.config(
                    text="Ready for batch automation", foreground="blue"))
                self.master.after(0, lambda: self.main_app.set_status_bar_text("Ready"))

                # Reset other UI elements
                self.master.after(0, lambda: self.plan_job_button.config(state=tk.NORMAL, text="🔎 Plan Next Job"))
                self.master.after(0, lambda: self.stop_button.config(state=tk.DISABLED))
                self.master.after(0, lambda: self.execute_job_button.config(state=tk.DISABLED, text="🚀 Execute Job"))
        
        # Start the batch thread
        self.current_thread = threading.Thread(target=batch_thread, daemon=True)
        self.current_thread.start()
    
    def stop_automated_batch(self):
        """Stop the automated batch processing."""
        if self.is_batch_running:
            self.stop_requested = True
            self.log_message("🛑 Stop requested for automated batch - will terminate after current job...", "warning")
            self.stop_automation_button.config(state=tk.DISABLED)
            self.automation_status_label.config(text="Stopping after current job...", foreground="red")
        else:
            messagebox.showinfo("No Batch Running", "No automated batch is currently running.")

    def update_database_instance(self, new_db, new_planner, new_worker):
        """Update database and backend component references."""
        self.db = new_db
        self.planner = new_planner
        self.worker = new_worker

    def reset_for_new_database(self):
        """Reset tab state for new database."""
        # Clear job-related state
        self.current_planned_job = None
        self.last_job_execution_result = None

        # Reset execution controls
        self.reset_execution_controls()

        # Refresh dropdowns and status
        self.populate_manual_entry_dropdowns()
        self.update_dashboard_status()
