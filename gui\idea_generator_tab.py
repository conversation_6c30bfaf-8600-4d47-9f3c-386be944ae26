"""
Idea Generator Tab - Content Strategist Dashboard

This module contains the IdeaGeneratorTab class which handles the idea generation
interface including LLM selection, generation settings, and results display.
"""

import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox
import threading
import json
from typing import List, Dict, Any, Optional

from core import config_manager
from core.llm_interface import AVAILABLE_MODELS


class IdeaGeneratorTab(ttk.Frame):
    """
    Idea Generator tab containing generation controls and results display.
    """

    def __init__(self, master, main_app, db, idea_generator, log_func, *args, **kwargs):
        super().__init__(master, *args, **kwargs)
        
        # Store references to main app components
        self.main_app = main_app
        self.db = db
        self.idea_generator = idea_generator
        self.log_message = log_func
        
        # Initialize variables
        self.is_running = False
        self.stop_requested = False
        
        # Generation control variables
        self.num_ideas_var = tk.IntVar(value=5)
        self.target_craft_var = tk.StringVar()
        self.target_pillar_var = tk.StringVar()
        self.target_vector_var = tk.StringVar()
        self.forced_ideas_var = tk.BooleanVar(value=False)
        
        # LLM selection variables
        self.idea_gen_llm_provider_var = tk.StringVar()
        self.idea_gen_llm_model_var = tk.StringVar()
        
        # Store craft-pillar mapping for dynamic pillar dropdown
        self.craft_pillar_map = {}
        
        # Create the idea generator UI
        self._create_widgets()
        
        # Load generation options
        self.load_generation_options()

    def _create_widgets(self):
        """Create the enhanced idea generator tab widgets with PanedWindow layout."""
        # Configure padding
        self.configure(padding="10")

        # Main PanedWindow (vertical) for resizable layout
        main_paned = ttk.PanedWindow(self, orient=tk.VERTICAL)
        main_paned.pack(fill=tk.BOTH, expand=True)

        # Settings & Controls Top Pane
        settings_controls_frame = ttk.Frame(main_paned)
        main_paned.add(settings_controls_frame, weight=1)

        # Notebook for organizing control groups
        self.control_notebook = ttk.Notebook(settings_controls_frame)
        self.control_notebook.pack(fill=tk.BOTH, expand=True, pady=(0, 10))

        # Tab 1: Generation Parameters
        self.create_generation_parameters_tab()

        # Tab 2: Strategy Inputs (View & Edit Links)
        self.create_strategy_inputs_tab()

        # Main Action Buttons (below the notebook)
        action_frame = ttk.Frame(settings_controls_frame)
        action_frame.pack(pady=10)

        self.generate_ideas_button = ttk.Button(action_frame, text="💡 Generate New Ideas", command=self.generate_ideas)
        self.generate_ideas_button.pack(side=tk.LEFT, padx=(0, 10))

        # Progress bar for idea generation (initially hidden)
        self.idea_progress = ttk.Progressbar(action_frame, mode='indeterminate', length=200)

        # Generated Ideas Results Bottom Pane
        results_frame = ttk.LabelFrame(main_paned, text="Generated Ideas Results", padding="8")
        main_paned.add(results_frame, weight=2)

        self.ideas_text = scrolledtext.ScrolledText(results_frame, wrap=tk.WORD)
        self.ideas_text.pack(fill=tk.BOTH, expand=True)

    def create_generation_parameters_tab(self):
        """Create the Generation Parameters tab."""
        params_frame = ttk.Frame(self.control_notebook, padding="10")
        self.control_notebook.add(params_frame, text="Generation Parameters")

        # Number of Ideas
        ideas_frame = ttk.Frame(params_frame)
        ideas_frame.pack(fill=tk.X, pady=5)
        ttk.Label(ideas_frame, text="Number of Ideas:").pack(side=tk.LEFT, padx=(0, 10))
        ttk.Spinbox(ideas_frame, from_=1, to=20, textvariable=self.num_ideas_var, width=8).pack(side=tk.LEFT)

        # Targeting section
        targeting_frame = ttk.LabelFrame(params_frame, text="Targeting", padding="8")
        targeting_frame.pack(fill=tk.X, pady=10)

        # Configure targeting frame grid for better layout
        targeting_frame.columnconfigure(1, weight=1)
        targeting_frame.columnconfigure(3, weight=1)

        # Target Craft
        ttk.Label(targeting_frame, text="Target Craft (optional):").grid(row=0, column=0, sticky="w", padx=(0, 8), pady=2)
        self.craft_combo = ttk.Combobox(targeting_frame, textvariable=self.target_craft_var, state="readonly")
        self.craft_combo.grid(row=0, column=1, sticky="ew", padx=(0, 15), pady=2)
        self.craft_combo.bind('<<ComboboxSelected>>', self.on_target_craft_selected)

        # Target Pillar (dynamic based on craft)
        ttk.Label(targeting_frame, text="Target Pillar (optional):").grid(row=0, column=2, sticky="w", padx=(0, 8), pady=2)
        self.pillar_combo = ttk.Combobox(targeting_frame, textvariable=self.target_pillar_var, state="readonly")
        self.pillar_combo.grid(row=0, column=3, sticky="ew", pady=2)

        # Target Creativity Vector
        ttk.Label(targeting_frame, text="Target Creativity Vector (optional):").grid(row=1, column=0, sticky="w", padx=(0, 8), pady=2)
        self.vector_combo = ttk.Combobox(targeting_frame, textvariable=self.target_vector_var, state="readonly")
        self.vector_combo.grid(row=1, column=1, columnspan=3, sticky="ew", pady=2)

        # LLM Configuration section
        llm_frame = ttk.LabelFrame(params_frame, text="LLM Configuration", padding="8")
        llm_frame.pack(fill=tk.X, pady=10)

        # Configure LLM frame grid for better layout
        llm_frame.columnconfigure(1, weight=1)
        llm_frame.columnconfigure(3, weight=2)

        # Provider selection
        ttk.Label(llm_frame, text="Idea Gen LLM Provider:").grid(row=0, column=0, sticky="w", padx=(0, 8), pady=2)
        self.idea_gen_provider_combo = ttk.Combobox(llm_frame, textvariable=self.idea_gen_llm_provider_var,
                                                   state="readonly")
        self.idea_gen_provider_combo.grid(row=0, column=1, sticky="ew", padx=(0, 15), pady=2)
        self.idea_gen_provider_combo.bind('<<ComboboxSelected>>', self.on_idea_gen_provider_changed)

        # Model selection
        ttk.Label(llm_frame, text="Idea Gen LLM Model:").grid(row=0, column=2, sticky="w", padx=(0, 8), pady=2)
        self.idea_gen_model_combo = ttk.Combobox(llm_frame, textvariable=self.idea_gen_llm_model_var,
                                                state="readonly")
        self.idea_gen_model_combo.grid(row=0, column=3, sticky="ew", pady=2)

        # Brand Focus Toggle
        brand_focus_frame = ttk.Frame(params_frame)
        brand_focus_frame.pack(fill=tk.X, pady=10)
        ttk.Checkbutton(brand_focus_frame, text="💡 Generate Ideas with Brand Focus",
                       variable=self.forced_ideas_var).pack(side=tk.LEFT)

    def create_strategy_inputs_tab(self):
        """Create the Strategy Inputs tab with view and edit links."""
        strategy_frame = ttk.Frame(self.control_notebook, padding="10")
        self.control_notebook.add(strategy_frame, text="Strategy Inputs (View & Edit Links)")

        # Business Pillars Display
        pillars_frame = ttk.LabelFrame(strategy_frame, text="Business Pillars", padding="8")
        pillars_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))

        ttk.Label(pillars_frame, text="Current Business Pillars (defined in main Settings):").pack(anchor=tk.W, pady=(0, 5))
        
        self.business_pillars_display = scrolledtext.ScrolledText(pillars_frame, wrap=tk.WORD, height=4, state=tk.DISABLED)
        self.business_pillars_display.pack(fill=tk.BOTH, expand=True, pady=(0, 5))
        
        ttk.Button(pillars_frame, text="Edit Business Pillars in Settings...", 
                  command=self.edit_business_pillars).pack()

        # Creativity Vectors Display
        vectors_frame = ttk.LabelFrame(strategy_frame, text="Creativity Vectors", padding="8")
        vectors_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))

        ttk.Label(vectors_frame, text="Current Creativity Vectors (defined in main Settings):").pack(anchor=tk.W, pady=(0, 5))
        
        self.creativity_vectors_display = scrolledtext.ScrolledText(vectors_frame, wrap=tk.WORD, height=3, state=tk.DISABLED)
        self.creativity_vectors_display.pack(fill=tk.BOTH, expand=True, pady=(0, 5))
        
        ttk.Button(vectors_frame, text="Edit Creativity Vectors in Settings...", 
                  command=self.edit_creativity_vectors).pack()

        # Idea Prompt Snippet Display
        prompt_frame = ttk.LabelFrame(strategy_frame, text="Idea Generation Prompt", padding="8")
        prompt_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))

        ttk.Label(prompt_frame, text="Idea Generation Prompt Snippet (full prompt in Settings):").pack(anchor=tk.W, pady=(0, 5))
        
        self.idea_prompt_display = scrolledtext.ScrolledText(prompt_frame, wrap=tk.WORD, height=3, state=tk.DISABLED)
        self.idea_prompt_display.pack(fill=tk.BOTH, expand=True, pady=(0, 5))
        
        ttk.Button(prompt_frame, text="Edit Full Idea Prompt in Settings...", 
                  command=self.edit_idea_prompt).pack()

        # Forced Ideas Prompt Display
        forced_prompt_frame = ttk.LabelFrame(strategy_frame, text="Forced Ideas Prompt", padding="8")
        forced_prompt_frame.pack(fill=tk.BOTH, expand=True)

        ttk.Label(forced_prompt_frame, text="Custom prompt used when 'Forced Ideas' is enabled (defined in Settings):").pack(anchor=tk.W, pady=(0, 5))
        
        self.forced_prompt_display = scrolledtext.ScrolledText(forced_prompt_frame, wrap=tk.WORD, height=3, state=tk.DISABLED)
        self.forced_prompt_display.pack(fill=tk.BOTH, expand=True, pady=(0, 5))
        
        ttk.Button(forced_prompt_frame, text="Edit Forced Ideas Prompt in Settings...", 
                  command=self.edit_forced_prompt).pack()

    def on_target_craft_selected(self, event=None):
        """Update the pillar dropdown based on selected craft."""
        selected_craft = self.target_craft_var.get()
        if selected_craft in self.craft_pillar_map:
            pillars = [''] + self.craft_pillar_map[selected_craft]  # Add empty option
            self.pillar_combo['values'] = pillars
            self.target_pillar_var.set('')  # Clear current selection
        else:
            self.pillar_combo['values'] = []
            self.target_pillar_var.set('')

    def edit_business_pillars(self):
        """Open settings window focused on Business Logic tab for editing business pillars."""
        self.main_app.open_settings()
        # After settings window closes, refresh displayed settings
        self.refresh_readonly_displays()

    def edit_creativity_vectors(self):
        """Open settings window focused on Business Logic tab for editing creativity vectors."""
        self.main_app.open_settings()
        # After settings window closes, refresh displayed settings
        self.refresh_readonly_displays()

    def edit_idea_prompt(self):
        """Open settings window focused on LLM Prompts tab for editing idea prompt."""
        self.main_app.open_settings()
        # After settings window closes, refresh displayed settings
        self.refresh_readonly_displays()

    def edit_forced_prompt(self):
        """Open settings window focused on Business Logic tab for editing forced ideas prompt."""
        self.main_app.open_settings()
        # After settings window closes, refresh displayed settings
        self.refresh_readonly_displays()

    def refresh_readonly_displays(self):
        """Refresh the read-only displays of settings from config."""
        try:
            # Refresh Business Pillars display
            business_pillars = config_manager.get_pipeline_setting('business_pillars') or ''
            self.business_pillars_display.config(state=tk.NORMAL)
            self.business_pillars_display.delete('1.0', tk.END)
            self.business_pillars_display.insert('1.0', business_pillars)
            self.business_pillars_display.config(state=tk.DISABLED)

            # Refresh Creativity Vectors display
            creativity_vectors = config_manager.get_creativity_vectors()
            vectors_text = '\n'.join(creativity_vectors) if creativity_vectors else 'No vectors configured'
            self.creativity_vectors_display.config(state=tk.NORMAL)
            self.creativity_vectors_display.delete('1.0', tk.END)
            self.creativity_vectors_display.insert('1.0', vectors_text)
            self.creativity_vectors_display.config(state=tk.DISABLED)

            # Refresh Idea Prompt display (show first 300 characters)
            idea_prompt = config_manager.get_prompt('idea_generator_prompt') or ''
            prompt_snippet = idea_prompt[:300] + '...' if len(idea_prompt) > 300 else idea_prompt
            self.idea_prompt_display.config(state=tk.NORMAL)
            self.idea_prompt_display.delete('1.0', tk.END)
            self.idea_prompt_display.insert('1.0', prompt_snippet)
            self.idea_prompt_display.config(state=tk.DISABLED)

            # Refresh Forced Ideas Prompt display
            forced_prompt = config_manager.get_forced_ideas_prompt() or 'No forced ideas prompt configured'
            self.forced_prompt_display.config(state=tk.NORMAL)
            self.forced_prompt_display.delete('1.0', tk.END)
            self.forced_prompt_display.insert('1.0', forced_prompt)
            self.forced_prompt_display.config(state=tk.DISABLED)

        except Exception as e:
            self.log_message(f"Error refreshing displayed settings: {e}", "error")

    def populate_llm_dropdowns(self):
        """Populate LLM provider and model dropdowns."""
        try:
            # Get available providers
            providers = list(AVAILABLE_MODELS.keys())

            # Populate provider dropdown
            self.idea_gen_provider_combo['values'] = providers

            # Set default provider from config
            default_idea_provider = config_manager.get_pipeline_setting('analysis_llm') or 'local'

            if default_idea_provider in providers:
                self.idea_gen_llm_provider_var.set(default_idea_provider)
                self.on_idea_gen_provider_changed()

        except Exception as e:
            self.log_message(f"Error populating LLM dropdowns: {e}", "error")

    def on_idea_gen_provider_changed(self, event=None):
        """Handle idea generation LLM provider selection change."""
        try:
            provider = self.idea_gen_llm_provider_var.get()
            if provider and provider in AVAILABLE_MODELS:
                models = AVAILABLE_MODELS[provider]
                self.idea_gen_model_combo['values'] = models

                # Set default model
                default_model = config_manager.get_pipeline_setting('analysis_llm_model')
                if default_model and default_model in models:
                    self.idea_gen_llm_model_var.set(default_model)
                elif models:
                    self.idea_gen_llm_model_var.set(models[0])

        except Exception as e:
            self.log_message(f"Error updating idea generation models: {e}", "error")

    def load_generation_options(self):
        """Load creativity vectors, crafts, and craft-pillar mapping."""
        try:
            # Populate LLM dropdowns
            self.populate_llm_dropdowns()
            
            # Load creativity vectors
            vectors = self.idea_generator.get_creativity_vectors()
            self.vector_combo['values'] = [''] + vectors

            # Load crafts from business pillars and build craft-pillar mapping
            business_pillars = config_manager.get_pipeline_setting('business_pillars') or ''
            crafts = []
            self.craft_pillar_map = {}
            
            for line in business_pillars.split('\n'):
                line = line.strip()
                if '=' in line:
                    craft, pillars_str = line.split('=', 1)
                    craft = craft.strip()
                    pillars = [p.strip() for p in pillars_str.split(',') if p.strip()]
                    
                    if craft and pillars:
                        crafts.append(craft)
                        self.craft_pillar_map[craft] = pillars

            self.craft_combo['values'] = [''] + crafts
            
            # Clear pillar combo initially
            self.pillar_combo['values'] = []
            
            # Update the read-only displays
            self.refresh_readonly_displays()

        except Exception as e:
            self.log_message(f"Error loading generation options: {e}", "error")

    def generate_ideas(self):
        """Generate new content ideas in a background thread."""
        if self.is_running:
            messagebox.showwarning("Operation Running", "An operation is already running. Please wait for it to complete.")
            return

        def generate_thread():
            try:
                # Set running state and update UI
                self.is_running = True
                self.stop_requested = False
                self.master.after(0, lambda: self.generate_ideas_button.config(state=tk.DISABLED, text="Generating Ideas..."))
                self.master.after(0, lambda: self.main_app.dashboard_tab.stop_button.config(state=tk.NORMAL))
                self.master.after(0, lambda: self.idea_progress.pack(side=tk.LEFT, padx=(0, 10)))
                self.master.after(0, lambda: self.idea_progress.start(10))
                self.master.after(0, lambda: self.main_app.set_status_bar_text("Working...", True))

                self.log_message("💡 Generating new content ideas...", "info")

                # Check for stop request
                if self.stop_requested:
                    self.log_message("🛑 Idea generation stopped by user", "warning")
                    return

                # Get settings
                num_ideas = self.num_ideas_var.get()
                target_craft = self.target_craft_var.get() or None
                target_pillar = self.target_pillar_var.get() or None
                target_vector = self.target_vector_var.get() or None
                forced_ideas = self.forced_ideas_var.get()

                # Get business pillars from settings
                business_pillars = config_manager.get_pipeline_setting('business_pillars')
                if not business_pillars:
                    self.log_message("❌ No business pillars configured. Please set them in Settings > Business Logic", "error")
                    return

                # Check for stop request
                if self.stop_requested:
                    self.log_message("🛑 Idea generation stopped by user", "warning")
                    return

                # Get selected LLM settings for idea generation
                idea_provider = self.idea_gen_llm_provider_var.get()
                idea_model = self.idea_gen_llm_model_var.get()

                if idea_provider and idea_model:
                    self.log_message(f"Using {idea_provider} ({idea_model}) for idea generation", "info")
                    # Override the idea generator's LLM settings
                    original_provider = config_manager.get_pipeline_setting('analysis_llm')
                    original_model = config_manager.get_pipeline_setting('analysis_llm_model')

                    # Temporarily update config for this generation
                    config_manager.update_setting('PIPELINE_SETTINGS', 'analysis_llm', idea_provider)
                    config_manager.update_setting('PIPELINE_SETTINGS', 'analysis_llm_model', idea_model)

                try:
                    # Check for stop request before generating
                    if self.stop_requested:
                        self.log_message("🛑 Idea generation stopped by user", "warning")
                        return

                    # Generate ideas
                    ideas = self.idea_generator.generate_ideas(
                        pillars_text=business_pillars,
                        target_craft=target_craft,
                        target_creativity_vector=target_vector,
                        target_pillar=target_pillar,
                        forced_ideas=forced_ideas,
                        num_ideas=num_ideas
                    )

                    # Check for stop request after generation
                    if self.stop_requested:
                        self.log_message("🛑 Idea generation stopped by user", "warning")
                        return

                    # Display results
                    results_text = f"Generated {len(ideas)} new content ideas:\n\n"
                    for i, idea in enumerate(ideas, 1):
                        results_text += f"{i}. {idea['keyword']}\n"
                        results_text += f"   Pillar: {idea['pillar']} | Craft: {idea['craft']}\n"
                        results_text += f"   Angle: {idea['proposed_angle']}\n\n"

                    self.master.after(0, lambda: self.display_generated_ideas(results_text))
                    self.log_message(f"✅ Successfully generated {len(ideas)} new ideas", "success")

                    # Refresh displays
                    self.master.after(0, self.main_app.refresh_content_plan)
                    self.master.after(0, self.main_app.update_dashboard_status)

                finally:
                    # Restore original LLM settings if they were overridden
                    if idea_provider and idea_model:
                        if original_provider:
                            config_manager.update_setting('PIPELINE_SETTINGS', 'analysis_llm', original_provider)
                        if original_model:
                            config_manager.update_setting('PIPELINE_SETTINGS', 'analysis_llm_model', original_model)

            except Exception as e:
                self.log_message(f"❌ Error generating ideas: {e}", "error")
            finally:
                # Reset running state and UI
                self.is_running = False
                self.stop_requested = False
                self.master.after(0, lambda: self.generate_ideas_button.config(state=tk.NORMAL, text="💡 Generate New Ideas"))
                self.master.after(0, lambda: self.main_app.dashboard_tab.stop_button.config(state=tk.DISABLED))
                self.master.after(0, lambda: self.idea_progress.stop())
                self.master.after(0, lambda: self.idea_progress.pack_forget())
                self.master.after(0, lambda: self.main_app.set_status_bar_text("Ready"))

        threading.Thread(target=generate_thread, daemon=True).start()

    def display_generated_ideas(self, text: str):
        """Display generated ideas in the text area."""
        self.ideas_text.delete('1.0', tk.END)
        self.ideas_text.insert('1.0', text)

    def stop_operations(self):
        """Stop any running operations."""
        if self.is_running:
            self.stop_requested = True
            self.log_message("🛑 Stop requested - idea generation will terminate soon...", "warning")
        else:
            messagebox.showinfo("No Operations", "No idea generation operations are currently running.")

    def update_database_instance(self, new_db, new_idea_generator):
        """Update database and backend component references."""
        self.db = new_db
        self.idea_generator = new_idea_generator

    def reset_for_new_database(self):
        """Reset tab state for new database."""
        # Clear ideas text
        self.ideas_text.delete('1.0', tk.END)

        # Reload generation options
        self.load_generation_options()
