#!/usr/bin/env python3
"""
Multi-Database Functionality Test Script

This script tests the multi-database support functionality including:
- Configuration management for multiple databases
- Database switching and creation
- Data isolation between databases
- UI component updates during database switches
"""

import sys
import os
import tempfile
import shutil
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from core import config_manager
from database import ContentDatabase


def cleanup_test_databases():
    """Clean up any test database entries from previous runs."""
    databases = config_manager.get_databases_config()
    for db_key, db_info in databases.items():
        if db_key != 'db1' and ('test' in db_info['name'].lower() or 'tmp' in db_info['path']):
            config_manager.remove_database_entry(db_key)
    config_manager.set_active_db_key('db1')

def test_config_management():
    """Test the configuration management functions."""
    print("🧪 Testing Configuration Management...")

    # Clean up any leftover test databases
    cleanup_test_databases()

    # Test getting databases config (should have default)
    databases = config_manager.get_databases_config()
    print(f"✓ Initial databases config: {databases}")
    assert 'db1' in databases, "Default database should exist"

    # Test getting active database path
    active_path = config_manager.get_active_db_path()
    print(f"✓ Active database path: {active_path}")
    assert active_path.endswith('content_ledger.db'), "Default path should be content_ledger.db"
    
    # Test adding a new database entry
    test_name = "Test Database"
    test_path = "/tmp/test_db.db"
    db_key = config_manager.add_database_entry(test_name, test_path)
    print(f"✓ Added new database with key: {db_key}")
    
    # Verify it was added
    databases = config_manager.get_databases_config()
    assert db_key in databases, "New database should be in config"
    assert databases[db_key]['name'] == test_name, "Database name should match"
    assert databases[db_key]['path'] == test_path, "Database path should match"
    
    # Test setting active database
    config_manager.set_active_db_key(db_key)
    new_active_path = config_manager.get_active_db_path()
    print(f"✓ New active database path: {new_active_path}")
    # The path might be converted to absolute, so check if it ends with the expected path
    assert test_path in new_active_path or new_active_path.endswith(test_path), "Active path should be updated"
    
    # Test removing database entry
    config_manager.remove_database_entry(db_key)
    databases = config_manager.get_databases_config()
    assert db_key not in databases, "Database should be removed from config"
    
    # Reset to default
    config_manager.set_active_db_key('db1')
    
    print("✅ Configuration management tests passed!")


def test_database_isolation():
    """Test that different database files maintain data isolation."""
    print("\n🧪 Testing Database Isolation...")

    # Create temporary directories for test databases
    temp_dir = tempfile.mkdtemp()
    try:
        db1_path = os.path.join(temp_dir, "test_db1.db")
        db2_path = os.path.join(temp_dir, "test_db2.db")
        
        # Create two database instances
        db1 = ContentDatabase(db_path=db1_path)
        db2 = ContentDatabase(db_path=db2_path)
        
        # Add different content to each database
        db1_id = db1.add_content_idea("test keyword 1", "pillar1", "craft1", "angle1")
        db2_id = db2.add_content_idea("test keyword 2", "pillar2", "craft2", "angle2")
        
        print(f"✓ Added content to DB1 (ID: {db1_id}) and DB2 (ID: {db2_id})")
        
        # Verify isolation - each database should only see its own content
        db1_content = db1.get_all_content()
        db2_content = db2.get_all_content()
        
        assert len(db1_content) == 1, "DB1 should have exactly 1 content item"
        assert len(db2_content) == 1, "DB2 should have exactly 1 content item"
        
        assert db1_content[0]['keyword'] == "test keyword 1", "DB1 should have its own content"
        assert db2_content[0]['keyword'] == "test keyword 2", "DB2 should have its own content"
        
        # Verify cross-contamination doesn't occur
        db1_keyword2 = db1.get_content_by_keyword("test keyword 2")
        db2_keyword1 = db2.get_content_by_keyword("test keyword 1")

        assert db1_keyword2 is None, "DB1 should not see DB2's content"
        assert db2_keyword1 is None, "DB2 should not see DB1's content"

        # Close database connections to allow cleanup
        del db1
        del db2

        print("✅ Database isolation tests passed!")

    finally:
        # Clean up temporary directory
        try:
            import time
            time.sleep(0.1)  # Give time for connections to close
            shutil.rmtree(temp_dir, ignore_errors=True)
        except Exception:
            pass  # Ignore cleanup errors


def test_database_creation_and_switching():
    """Test database creation and switching functionality."""
    print("\n🧪 Testing Database Creation and Switching...")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        # Test creating new database configurations
        db1_path = os.path.join(temp_dir, "project1.db")
        db2_path = os.path.join(temp_dir, "project2.db")
        
        # Add database configurations
        db1_key = config_manager.add_database_entry("Project 1 Database", db1_path)
        db2_key = config_manager.add_database_entry("Project 2 Database", db2_path)
        
        print(f"✓ Created database configs: {db1_key}, {db2_key}")
        
        # Test switching between databases
        config_manager.set_active_db_key(db1_key)
        active_path = config_manager.get_active_db_path()
        assert active_path == db1_path, "Should switch to DB1"
        
        config_manager.set_active_db_key(db2_key)
        active_path = config_manager.get_active_db_path()
        assert active_path == db2_path, "Should switch to DB2"
        
        # Test that databases are created when accessed
        db1_instance = ContentDatabase(db_path=db1_path)
        db2_instance = ContentDatabase(db_path=db2_path)
        
        assert os.path.exists(db1_path), "DB1 file should be created"
        assert os.path.exists(db2_path), "DB2 file should be created"
        
        # Add different content to verify they're separate
        db1_instance.add_content_idea("project1 keyword", "pillar1", "craft1")
        db2_instance.add_content_idea("project2 keyword", "pillar2", "craft2")
        
        # Verify content separation
        db1_stats = db1_instance.get_content_stats()
        db2_stats = db2_instance.get_content_stats()

        assert db1_stats.get('NEW', 0) == 1, "DB1 should have 1 NEW item"
        assert db2_stats.get('NEW', 0) == 1, "DB2 should have 1 NEW item"

        # Close database connections
        del db1_instance
        del db2_instance

        # Clean up configurations
        config_manager.remove_database_entry(db1_key)
        config_manager.remove_database_entry(db2_key)
        
        print("✅ Database creation and switching tests passed!")


def test_backend_component_initialization():
    """Test that backend components can be initialized with database instances."""
    print("\n🧪 Testing Backend Component Initialization...")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        db_path = os.path.join(temp_dir, "test_backend.db")
        
        # Create database instance
        db = ContentDatabase(db_path=db_path)
        
        # Test importing and initializing backend components
        try:
            from planner import ContentPlanner
            from worker import ContentWorker
            from idea_generator import IdeaGenerator
            
            # Test initialization with database instance
            planner = ContentPlanner(db_instance=db)
            worker = ContentWorker(db_instance=db)
            idea_gen = IdeaGenerator(db_instance=db)
            
            # Verify they use the same database
            assert planner.db.db_path == db_path, "Planner should use provided database"
            assert worker.db.db_path == db_path, "Worker should use provided database"
            assert idea_gen.db.db_path == db_path, "IdeaGenerator should use provided database"
            
            print("✓ Backend components initialized with custom database")
            
            # Test fallback to default database
            planner_default = ContentPlanner()
            worker_default = ContentWorker()
            idea_gen_default = IdeaGenerator()
            
            print("✓ Backend components can initialize with default database")
            
        except ImportError as e:
            print(f"⚠️ Could not test backend components: {e}")
            
    print("✅ Backend component initialization tests passed!")


def test_error_handling():
    """Test error handling for edge cases."""
    print("\n🧪 Testing Error Handling...")
    
    # Test with non-existent database key
    config_manager.set_active_db_key("non_existent_key")
    active_path = config_manager.get_active_db_path()
    print(f"✓ Fallback path for non-existent key: {active_path}")
    
    # Test with invalid database path
    try:
        invalid_path = "/invalid/path/that/does/not/exist/test.db"
        db = ContentDatabase(db_path=invalid_path)
        # Should create directories and work
        print("✓ Database handles invalid paths by creating directories")
    except Exception as e:
        print(f"⚠️ Database creation failed as expected: {e}")
    
    # Reset to default
    config_manager.set_active_db_key('db1')
    
    print("✅ Error handling tests passed!")


def main():
    """Run all multi-database functionality tests."""
    print("🚀 Starting Multi-Database Functionality Tests")
    print("=" * 50)
    
    try:
        test_config_management()
        test_database_isolation()
        test_database_creation_and_switching()
        test_backend_component_initialization()
        test_error_handling()
        
        print("\n" + "=" * 50)
        print("🎉 All multi-database functionality tests passed!")
        print("\nThe multi-database feature is ready for use:")
        print("• Users can create and switch between multiple databases")
        print("• Each database maintains complete data isolation")
        print("• Backend components properly reinitialize with new databases")
        print("• Configuration management handles edge cases gracefully")
        
        return True
        
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
