"""
Database Switcher Dialog - Content Strategist

This dialog allows users to switch between different database files,
add new database connections, and remove existing ones.
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog, simpledialog
import os
from typing import Dict, Any, Optional

from core import config_manager


class DatabaseSwitcherDialog(tk.Toplevel):
    """
    Dialog for managing and switching between multiple databases.
    """
    
    def __init__(self, master, main_app_instance):
        super().__init__(master)
        self.main_app = main_app_instance
        
        # Configure dialog
        self.title("Database Manager")
        self.geometry("600x400")
        self.resizable(True, True)
        self.transient(master)
        self.grab_set()
        
        # Center the dialog
        self.center_dialog()
        
        # Create UI
        self.create_widgets()
        
        # Load database list
        self.refresh_database_list()
        
    def center_dialog(self):
        """Center the dialog on the parent window."""
        self.update_idletasks()
        x = (self.master.winfo_x() + (self.master.winfo_width() // 2) - (self.winfo_width() // 2))
        y = (self.master.winfo_y() + (self.master.winfo_height() // 2) - (self.winfo_height() // 2))
        self.geometry(f"+{x}+{y}")
        
    def create_widgets(self):
        """Create the dialog widgets."""
        # Main frame
        main_frame = ttk.Frame(self, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Title
        title_label = ttk.Label(main_frame, text="Database Manager", font=("Arial", 14, "bold"))
        title_label.pack(pady=(0, 10))
        
        # Database list frame
        list_frame = ttk.LabelFrame(main_frame, text="Available Databases", padding="10")
        list_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        
        # Create treeview for database list
        columns = ('Name', 'Path', 'Status')
        self.db_tree = ttk.Treeview(list_frame, columns=columns, show='headings', height=8)
        
        # Configure columns
        self.db_tree.heading('Name', text='Database Name')
        self.db_tree.heading('Path', text='File Path')
        self.db_tree.heading('Status', text='Status')
        
        self.db_tree.column('Name', width=200)
        self.db_tree.column('Path', width=300)
        self.db_tree.column('Status', width=80)
        
        # Scrollbar for treeview
        scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.db_tree.yview)
        self.db_tree.configure(yscrollcommand=scrollbar.set)
        
        # Pack treeview and scrollbar
        self.db_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # Button frame
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=(0, 10))
        
        # Buttons
        ttk.Button(button_frame, text="Activate Selected", command=self.activate_selected).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="Add New Database...", command=self.add_new_database).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="Remove Selected", command=self.remove_selected).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="Refresh", command=self.refresh_database_list).pack(side=tk.LEFT, padx=(0, 5))
        
        # Close button frame
        close_frame = ttk.Frame(main_frame)
        close_frame.pack(fill=tk.X)
        
        ttk.Button(close_frame, text="Close", command=self.destroy).pack(side=tk.RIGHT)
        
    def refresh_database_list(self):
        """Refresh the database list display."""
        # Clear existing items
        for item in self.db_tree.get_children():
            self.db_tree.delete(item)
        
        # Get database configurations
        databases = config_manager.get_databases_config()
        active_db_key = config_manager.load_config().get('APPLICATION_SETTINGS', 'active_db_key', fallback=None)
        
        # Populate treeview
        for db_key, db_info in databases.items():
            status = "Active" if db_key == active_db_key else "Available"
            
            # Check if file exists
            if not os.path.exists(db_info['path']):
                status = "Missing"
            
            self.db_tree.insert('', tk.END, values=(db_info['name'], db_info['path'], status), tags=(db_key,))
        
        # If no databases, show a message
        if not databases:
            self.db_tree.insert('', tk.END, values=("No databases configured", "", ""))
            
    def activate_selected(self):
        """Activate the selected database."""
        selection = self.db_tree.selection()
        if not selection:
            messagebox.showwarning("No Selection", "Please select a database to activate.")
            return
        
        # Get the selected database key
        item = selection[0]
        db_key = self.db_tree.item(item)['tags'][0] if self.db_tree.item(item)['tags'] else None
        
        if not db_key:
            messagebox.showerror("Error", "Invalid database selection.")
            return
        
        # Check if operations are running
        if hasattr(self.main_app, 'is_batch_running') and self.main_app.is_batch_running:
            messagebox.showerror("Operation in Progress", "Cannot switch databases while a batch operation is running.")
            return
        
        if hasattr(self.main_app, 'is_running') and self.main_app.is_running:
            messagebox.showerror("Operation in Progress", "Cannot switch databases while an operation is running.")
            return
        
        # Get the database path
        databases = config_manager.get_databases_config()
        if db_key not in databases:
            messagebox.showerror("Error", "Selected database configuration not found.")
            return
        
        new_path = databases[db_key]['path']
        
        # Check if file exists
        if not os.path.exists(new_path):
            result = messagebox.askyesno("Database Missing", 
                                       f"The database file does not exist:\n{new_path}\n\n"
                                       "Do you want to create a new database at this location?")
            if not result:
                return
        
        try:
            # Set as active database
            config_manager.set_active_db_key(db_key)
            
            # Reinitialize the main application with new database
            self.main_app.reinitialize_with_new_database(new_path)
            
            # Refresh the list to show new active status
            self.refresh_database_list()
            
            messagebox.showinfo("Success", f"Successfully switched to database:\n{databases[db_key]['name']}")
            
        except Exception as e:
            messagebox.showerror("Error", f"Failed to switch database:\n{str(e)}")
    
    def add_new_database(self):
        """Add a new database configuration."""
        # Get database name
        name = simpledialog.askstring("Database Name", "Enter a name for the new database:")
        if not name:
            return
        
        # Get database file path
        path = filedialog.asksaveasfilename(
            title="Choose Database File Location",
            defaultextension=".db",
            filetypes=[("SQLite Database", "*.db"), ("All Files", "*.*")]
        )
        if not path:
            return
        
        try:
            # Add database entry
            db_key = config_manager.add_database_entry(name, path)
            
            # Ask if user wants to activate this database now
            result = messagebox.askyesno("Activate Database", 
                                       f"Database '{name}' added successfully.\n\n"
                                       "Do you want to activate this database now?")
            
            if result:
                # Check if operations are running
                if hasattr(self.main_app, 'is_batch_running') and self.main_app.is_batch_running:
                    messagebox.showerror("Operation in Progress", "Cannot switch databases while a batch operation is running.")
                elif hasattr(self.main_app, 'is_running') and self.main_app.is_running:
                    messagebox.showerror("Operation in Progress", "Cannot switch databases while an operation is running.")
                else:
                    # Set as active and reinitialize
                    config_manager.set_active_db_key(db_key)
                    self.main_app.reinitialize_with_new_database(path)
            
            # Refresh the list
            self.refresh_database_list()
            
        except Exception as e:
            messagebox.showerror("Error", f"Failed to add database:\n{str(e)}")
    
    def remove_selected(self):
        """Remove the selected database configuration."""
        selection = self.db_tree.selection()
        if not selection:
            messagebox.showwarning("No Selection", "Please select a database to remove.")
            return
        
        # Get the selected database info
        item = selection[0]
        values = self.db_tree.item(item)['values']
        db_key = self.db_tree.item(item)['tags'][0] if self.db_tree.item(item)['tags'] else None
        
        if not db_key or not values:
            messagebox.showerror("Error", "Invalid database selection.")
            return
        
        db_name = values[0]
        
        # Confirm removal
        result = messagebox.askyesno("Confirm Removal", 
                                   f"Are you sure you want to remove the connection to '{db_name}'?\n\n"
                                   "This will not delete the database file, only remove it from the list.")
        if not result:
            return
        
        try:
            # Remove database entry
            config_manager.remove_database_entry(db_key)
            
            # Refresh the list
            self.refresh_database_list()
            
            messagebox.showinfo("Success", f"Database connection '{db_name}' removed successfully.")
            
        except Exception as e:
            messagebox.showerror("Error", f"Failed to remove database:\n{str(e)}")
